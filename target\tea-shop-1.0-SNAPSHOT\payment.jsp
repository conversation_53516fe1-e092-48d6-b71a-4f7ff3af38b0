<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单支付 - 茶品商城</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        body {
            background-color: #f8f9fa;
            color: #333;
            line-height: 1.6;
            padding: 20px;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            overflow: hidden;
        }
        .header {
            background-color: #2c3e50;
            color: white;
            padding: 20px 30px;
            text-align: center;
        }
        .header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }
        .order-info {
            padding: 30px;
            border-bottom: 1px solid #f0f0f0;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            padding: 10px 0;
            border-bottom: 1px dashed #eee;
        }
        .info-row:last-child {
            border-bottom: none;
            font-size: 18px;
            font-weight: bold;
            color: #e74c3c;
        }
        .payment-section {
            padding: 30px;
        }
        .payment-methods {
            margin-bottom: 30px;
        }
        .payment-methods h3 {
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .payment-option {
            display: flex;
            align-items: center;
            padding: 15px;
            border: 2px solid #f0f0f0;
            border-radius: 8px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .payment-option:hover {
            border-color: #3498db;
            background-color: #f8f9fa;
        }
        .payment-option.selected {
            border-color: #3498db;
            background-color: #e3f2fd;
        }
        .payment-option input[type="radio"] {
            margin-right: 10px;
        }
        .payment-icon {
            width: 30px;
            height: 30px;
            margin-right: 10px;
            background-size: contain;
        }
        .btn {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
        }
        .btn-primary {
            background-color: #3498db;
            color: white;
        }
        .btn-primary:hover {
            background-color: #2980b9;
            transform: translateY(-2px);
        }
        .btn-secondary {
            background-color: #95a5a6;
            color: white;
            margin-top: 15px;
        }
        .btn-secondary:hover {
            background-color: #7f8c8d;
        }
        .back-links {
            padding: 20px 30px;
            text-align: center;
            border-top: 1px solid #f0f0f0;
        }
        .back-links a {
            display: inline-block;
            margin: 0 10px;
            padding: 8px 16px;
            text-decoration: none;
            color: #3498db;
            border: 1px solid #3498db;
            border-radius: 4px;
            transition: all 0.3s;
        }
        .back-links a:hover {
            background-color: #3498db;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>订单支付</h1>
            <p>请选择支付方式完成付款</p>
        </div>

        <div class="order-info">
            <div class="info-row">
                <span>订单编号:</span>
                <span>${order.orderId}</span>
            </div>
            <div class="info-row">
                <span>下单时间:</span>
                <span>${order.orderDate}</span>
            </div>
            <div class="info-row">
                <span>支付金额:</span>
                <span>¥${order.totalAmount}</span>
            </div>
        </div>

        <div class="payment-section">
            <form action="/payment" method="post" class="payment-form">
                <input type="hidden" name="action" value="process">
                <input type="hidden" name="orderId" value="${order.orderId}">

                <div class="payment-methods">
                    <h3>选择支付方式</h3>
                    <label class="payment-option">
                        <input type="radio" name="paymentType" value="1" checked>
                        <span>💰 支付宝</span>
                    </label>
                    <label class="payment-option">
                        <input type="radio" name="paymentType" value="2">
                        <span>💚 微信支付</span>
                    </label>
                    <label class="payment-option">
                        <input type="radio" name="paymentType" value="3">
                        <span>💳 银联支付</span>
                    </label>
                </div>

                <button type="submit" class="btn btn-primary">确认支付 ¥${order.totalAmount}</button>
            </form>
        </div>

        <div class="back-links">
            <a href="/order?action=view&orderId=${order.orderId}">返回订单详情</a>
            <a href="/order?action=list">我的订单</a>
        </div>
    </div>

    <script>
        // 支付方式选择效果
        document.querySelectorAll('.payment-option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.payment-option').forEach(opt => opt.classList.remove('selected'));
                this.classList.add('selected');
                this.querySelector('input[type="radio"]').checked = true;
            });
        });

        // 默认选中第一个
        document.querySelector('.payment-option').classList.add('selected');
    </script>
</body>
</html>