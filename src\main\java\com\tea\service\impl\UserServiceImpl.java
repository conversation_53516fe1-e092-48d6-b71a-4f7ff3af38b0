package com.tea.service.impl;

import com.tea.dao.UserDao;
import com.tea.entity.User;
import com.tea.service.UserService;
import java.sql.SQLException;

public class UserServiceImpl implements UserService {
    private UserDao userDao = new UserDao();

    @Override
    public User login(String username, String password) throws SQLException {
        // 调用UserDao验证用户名密码
        return userDao.getUserByUsernameAndPassword(username, password);
    }

    @Override
    public boolean checkUsernameExists(String username) throws SQLException {
        // 检查用户名是否已存在
        return userDao.checkUsernameExists(username);
    }

    @Override
    public boolean register(User user) throws SQLException {
        // 调用UserDao插入新用户
        return userDao.insert(user);
    }
}