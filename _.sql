-- Fix for comment submission foreign key constraint issue
-- Execute this script on your existing teashop database

USE teashop;

-- First, drop the foreign key constraint
ALTER TABLE review DROP FOREIGN KEY fk_review_order;

-- Modify the order_id column to allow NULL values
ALTER TABLE review MODIFY COLUMN order_id bigint NULL DEFAULT NULL COMMENT '订单ID(可为空，表示非订单评论)';

-- Re-add the foreign key constraint with NULL support
ALTER TABLE review ADD CONSTRAINT fk_review_order 
FOREIGN KEY (order_id) REFERENCES `order` (order_id) 
ON DELETE SET NULL ON UPDATE RESTRICT;

-- Update any existing records with order_id = 0 to NULL
UPDATE review SET order_id = NULL WHERE order_id = 0;

-- Verify the changes
DESCRIBE review;
SHOW CREATE TABLE review;
