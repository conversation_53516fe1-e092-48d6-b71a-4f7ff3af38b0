package com.tea.service;

import com.tea.entity.Order;
import com.tea.entity.OrderItem;

import java.util.List;


public interface OrderService {
    String createOrder(Order order, List<OrderItem> cart) throws Exception;
    Order getOrderById(long orderId) throws Exception;
    List<Order> getOrdersByUserId(long userId) throws Exception;
    int updateOrderStatus(long orderId, int status) throws Exception;
}