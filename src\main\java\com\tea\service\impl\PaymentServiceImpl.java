package com.tea.service.impl;
import com.tea.dao.OrderDao;
import com.tea.entity.Order;
import com.tea.entity.User;
import com.tea.service.OrderService;
import com.tea.service.PaymentService;
import com.tea.service.impl.OrderServiceImpl;
import java.sql.Connection;
import java.sql.SQLException;
import com.tea.util.DBUtil;

public class PaymentServiceImpl implements PaymentService {
    private final OrderDao orderDao = new OrderDao();
    private final OrderService orderService = new OrderServiceImpl();

    @Override
    public String getPaymentStatus(Long orderId) throws Exception {
        Order order = orderDao.getOrderById(orderId);
        if (order == null) {
            return "订单不存在";
        }

        // 根据订单状态返回支付状态
        switch (order.getStatus()) {
            case 0:
                return "待付款";
            case 1:
                return "待发货";
            case 2:
                return "已发货";
            case 3:
                return "已完成";
            case 4:
                return "已取消";
            default:
                return "未知状态";
        }
    }

    @Override
    public boolean processPayment(Long orderId, User user) throws Exception {
        Connection conn = null;
        try {
            conn = DBUtil.getConnection();
            conn.setAutoCommit(false);

            // 查询订单信息
            Order order = orderDao.getOrderById(orderId);
            if (order == null) {
                throw new Exception("订单不存在");
            }

            // 验证订单归属
            if (order.getUserId() != user.getId()) {
                throw new Exception("无权操作此订单");
            }

            // 验证订单状态
            if (order.getStatus() != 0) {
                throw new Exception("订单状态异常，当前状态: " + order.getStatus());
            }

            // 模拟支付处理（实际项目中应集成第三方支付API）
            // 这里可以添加实际支付处理逻辑
            boolean paymentSuccess = true; // 假设支付成功

            if (paymentSuccess) {
                // 更新订单状态为待发货
                orderDao.updateOrderStatus(orderId, 1); // 1表示待发货状态
                conn.commit();
                return true;
            } else {
                conn.rollback();
                return false;
            }
        } catch (Exception e) {
            if (conn != null) {
                try { conn.rollback(); } catch (SQLException ex) {}
            }
            throw new Exception("支付处理失败: " + e.getMessage());
        } finally {
            if (conn != null) {
                try { conn.close(); } catch (SQLException e) {}
            }
        }
    }

    @Override
    public String verifyPayment(Long orderId) throws Exception {
        // 实现支付验证逻辑
        Order order = orderDao.getOrderById(orderId);
        if (order == null) {
            return "订单不存在";
        }

        // 模拟支付验证逻辑
        if (order.getStatus() == 1) {
            return "支付成功，订单已确认";
        } else if (order.getStatus() == 0) {
            return "支付尚未完成，请完成支付";
        } else {
            return "订单状态异常: " + getStatusText(order.getStatus());
        }
    }

    @Override
    public Order getOrderById(Long orderId) throws Exception {
        return orderDao.getOrderById(orderId);
    }

    private String getStatusText(int status) {
        switch (status) {
            case 0: return "待付款";
            case 1: return "待发货";
            case 2: return "已发货";
            case 3: return "已完成";
            case 4: return "已取消";
            default: return "未知状态";
        }
    }
}