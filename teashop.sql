/*
 Navicat Premium Dump SQL

 Source Server         : csp
 Source Server Type    : MySQL
 Source Server Version : 80041 (8.0.41)
 Source Host           : localhost:3306
 Source Schema         : teashop

 Target Server Type    : MySQL
 Target Server Version : 80041 (8.0.41)
 File Encoding         : 65001

 Date: 25/06/2025 22:26:45
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for admin
-- ----------------------------
DROP TABLE IF EXISTS `admin`;
CREATE TABLE `admin`  (
  `admin_id` bigint NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码(加密存储)',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '真实姓名',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '手机号',
  PRIMARY KEY (`admin_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '管理员表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of admin
-- ----------------------------
INSERT INTO `admin` VALUES (1, 'admin_encrypted_password', '王小华', '<EMAIL>', '17655454343');
INSERT INTO `admin` VALUES (2, 'admin_encrypted_password', '李小明', '<EMAIL>', '19877765655');
INSERT INTO `admin` VALUES (3, 'admin_encrypted_password', '邓典果', '<EMAIL>', '10809098876');
INSERT INTO `admin` VALUES (4, 'admin_encrypted_password', '李艺洁', '<EMAIL>', '14577673423');
INSERT INTO `admin` VALUES (5, 'admin_encrypted_password', '张三', '<EMAIL>', '18766565454');
INSERT INTO `admin` VALUES (6, 'admin_encrypted_password', '里斯', '<EMAIL>', '14343256654');

-- ----------------------------
-- Table structure for cart
-- ----------------------------
DROP TABLE IF EXISTS `cart`;
CREATE TABLE `cart`  (
  `cart_id` bigint NOT NULL AUTO_INCREMENT COMMENT '购物车ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `quantity` int NOT NULL DEFAULT 1 COMMENT '数量',
  `selected` tinyint NOT NULL DEFAULT 1 COMMENT '是否选中(0-未选 1-选中)',
  PRIMARY KEY (`cart_id`) USING BTREE,
  UNIQUE INDEX `idx_user_product`(`user_id` ASC, `product_id` ASC) USING BTREE,
  INDEX `idx_product_id`(`product_id` ASC) USING BTREE,
  CONSTRAINT `fk_cart_product` FOREIGN KEY (`product_id`) REFERENCES `product` (`product_id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_cart_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '购物车表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of cart
-- ----------------------------
INSERT INTO `cart` VALUES (2, 2, 2, 1, 1);
INSERT INTO `cart` VALUES (8, 1, 24, 1, 1);
INSERT INTO `cart` VALUES (10, 1, 13, 3, 1);
INSERT INTO `cart` VALUES (11, 1, 23, 1, 1);

-- ----------------------------
-- Table structure for category
-- ----------------------------
DROP TABLE IF EXISTS `category`;
CREATE TABLE `category`  (
  `category_id` bigint NOT NULL AUTO_INCREMENT COMMENT '类别ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '类别名称',
  PRIMARY KEY (`category_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '茶叶类别表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of category
-- ----------------------------
INSERT INTO `category` VALUES (1, '绿茶');
INSERT INTO `category` VALUES (2, '红茶');
INSERT INTO `category` VALUES (3, '白茶');
INSERT INTO `category` VALUES (4, '花茶');

-- ----------------------------
-- Table structure for order
-- ----------------------------
DROP TABLE IF EXISTS `order`;
CREATE TABLE `order`  (
  `order_id` bigint NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `total_amount` decimal(10, 2) NOT NULL COMMENT '订单总金额',
  `payment_amount` decimal(10, 2) NULL DEFAULT NULL COMMENT '实付金额',
  `freight_amount` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '运费',
  `discount_amount` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '优惠金额',
  `payment_type` tinyint NULL DEFAULT NULL COMMENT '支付方式(1-支付宝 2-微信 3-银联)',
  `payment_time` datetime NULL DEFAULT NULL COMMENT '支付时间',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '订单状态(0-待付款 1-待发货 2-待收货 3-已完成 4-已取消 5-已退款)',
  `shipping_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '收货人姓名',
  `shipping_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '收货人电话',
  `shipping_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '收货地址',
  `note` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '订单备注',
  `confirm_time` datetime NULL DEFAULT NULL COMMENT '确认收货时间',
  `order_date` datetime NOT NULL,
  PRIMARY KEY (`order_id` DESC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  CONSTRAINT `fk_order_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1750860726625 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订单表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of order
-- ----------------------------

-- ----------------------------
-- Table structure for order_item
-- ----------------------------
DROP TABLE IF EXISTS `order_item`;
CREATE TABLE `order_item`  (
  `item_id` bigint NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `quantity` int NOT NULL COMMENT '数量',
  `total_price` decimal(10, 2) NOT NULL COMMENT '总价',
  PRIMARY KEY (`item_id`) USING BTREE,
  INDEX `idx_order_id`(`order_id` ASC) USING BTREE,
  INDEX `idx_product_id`(`product_id` ASC) USING BTREE,
  CONSTRAINT `fk_order_item_order` FOREIGN KEY (`order_id`) REFERENCES `order` (`order_id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_order_item_product` FOREIGN KEY (`product_id`) REFERENCES `product` (`product_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订单明细表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of order_item
-- ----------------------------

-- ----------------------------
-- Table structure for product
-- ----------------------------
DROP TABLE IF EXISTS `product`;
CREATE TABLE `product`  (
  `product_id` bigint NOT NULL AUTO_INCREMENT COMMENT '商品ID',
  `category_id` bigint NULL DEFAULT NULL COMMENT '鎵€灞炵被鍒獻D',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '鍟嗗搧鍚嶇О',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '详细描述',
  `price` decimal(10, 2) NULL DEFAULT NULL COMMENT '浠锋牸',
  `stock` int NULL DEFAULT 0 COMMENT '搴撳瓨閲?',
  `sales` int NULL DEFAULT 0 COMMENT '閿€閲?',
  `weight` int NULL DEFAULT NULL COMMENT '重量(克)',
  `shelf_life` int NULL DEFAULT NULL COMMENT '保质期(月)',
  `main_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '涓诲浘URL',
  PRIMARY KEY (`product_id`) USING BTREE,
  INDEX `idx_category_id`(`category_id` ASC) USING BTREE,
  CONSTRAINT `fk_product_category` FOREIGN KEY (`category_id`) REFERENCES `category` (`category_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 40 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '茶叶商品表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of product
-- ----------------------------
INSERT INTO `product` VALUES (1, 1, '碧螺春', '来自中国江苏省的优质绿茶', 150.00, 250, 50, 100, 12, '/images/碧螺春.jpg');
INSERT INTO `product` VALUES (2, 2, '正山小种', '福建特产，世界著名的红茶之一', 200.00, 250, 40, 100, 18, '/images/正山小种.jpg');
INSERT INTO `product` VALUES (3, 1, '西湖龙井', '西湖龙井茶，产自浙江杭州西湖周边，扁平光滑，色泽翠绿。以“色绿、香郁、味甘、形美”四绝著称，具有鲜爽清香的独特风味', 60.00, 250, 1000, 100, 12, '/images/西湖龙井.jpg');
INSERT INTO `product` VALUES (4, 1, '太平猴魁', '太平猴魁，安徽黄山特产绿茶，叶片硕大而匀整，色泽苍绿匀润。其汤色清澈明亮，香气浓郁持久，口感鲜醇甘甜。', 70.00, 250, 200, 100, 12, '/images/太平猴魁.jpg');
INSERT INTO `product` VALUES (5, 1, '黄山毛峰', '黄山毛峰，产于安徽黄山地区，属于烘青绿茶。条索细扁微曲，白毫显露，芽叶肥壮，香味清雅持久，滋味鲜浓回味甘甜', 60.00, 250, 300, 100, 12, '/images/黄山毛峰.jpg');
INSERT INTO `product` VALUES (6, 1, '信阳毛尖', '信阳毛尖，河南信阳特产，属绿茶类。外形紧直圆滑，色泽翠绿光润，香气清爽持久，味道鲜醇爽口，叶底嫩绿均匀。', 50.00, 250, 56, 100, 12, '/images/信阳毛尖.jpg');
INSERT INTO `product` VALUES (7, 1, '都匀毛尖', '都匀毛尖是贵州特产，形状挺直略扁，色泽绿中带黄。具有独特的板栗香气，滋味浓厚甘醇，饮后余味悠长。', 66.00, 250, 34, 100, 12, '/images/都匀毛尖.jpg');
INSERT INTO `product` VALUES (8, 1, '刘安瓜片', '六安瓜片，安徽省六安市特产，无芽无梗，由单片生叶制成。外形似瓜子，色泽宝绿，汤色清澈，香气独特，味道醇厚', 88.00, 250, 222, 100, 12, '/images/刘安瓜片.jpg');
INSERT INTO `product` VALUES (9, 1, '蒙顶甘露', '蒙顶甘露，四川名茶，产自蒙顶山。外形紧结多毫，色泽嫩绿油润，香气馥郁芬芳，滋味鲜爽甘甜，有明显的花香气息。', 79.00, 250, 212, 100, 12, '/images/蒙顶甘露.jpg');
INSERT INTO `product` VALUES (10, 1, '安吉白茶', '安吉白茶，浙江省安吉县特产，虽名为白茶实为绿茶。叶片玉白色，形态优美，香气鲜爽持久，滋味清淡回甘', 80.00, 250, 1221, 100, 12, '/images/安吉白茶.jpg');
INSERT INTO `product` VALUES (11, 1, '恩施玉露', '恩施玉露，湖北恩施特产，蒸青绿茶。外形条索紧细圆直，色泽绿润，香气清新，滋味鲜爽甘甜，具有独特的兰花香。', 50.00, 250, 5000, 100, 12, '/images/恩施玉露.jpg');
INSERT INTO `product` VALUES (12, 2, '祁门红茶', '祁门红茶，世界四大红茶之一，产于安徽祁门。外形乌黑油润，金毫显露，香气独特，被誉为“祁门香”，滋味醇厚甘甜。', 200.00, 500, 345, 100, 12, '/images/祁门红茶.jpg');
INSERT INTO `product` VALUES (13, 2, '正山小种', '正山小种，福建武夷山特产红茶，开创了中国红茶的历史。烟熏味独特，带有桂圆香，滋味醇厚甘甜，极具特色。', 320.00, 500, 222, 100, 12, '/images/正山小种.jpg');
INSERT INTO `product` VALUES (14, 2, '金俊眉', '金俊眉，高端红茶代表，产自武夷山自然保护区。外形细长弯曲，金毫显露，香气高雅，滋味醇厚甜美，韵味悠长。', 200.00, 500, 100, 100, 12, '/images/金俊眉.jpg');
INSERT INTO `product` VALUES (15, 2, '银俊眉', '银俊眉，与金俊眉同源，但采用不同季节或部位的原料制作。色泽银亮，香气清新优雅，滋味鲜爽甜润，品质卓越。', 100.00, 500, 900, 100, 12, '/images/银俊眉.jpg');
INSERT INTO `product` VALUES (16, 2, '九曲红眉', '九曲红梅，浙江杭州传统红茶，因产地靠近西湖九曲岭得名。外形乌润紧结，香气芬芳馥郁，滋味鲜爽甘醇，独具风格。', 150.00, 500, 800, 100, 12, '/images/九曲红眉.jpg');
INSERT INTO `product` VALUES (17, 2, '宁红功夫', '宁红功夫，江西修水特产红茶，历史悠久。外形条索紧结，色泽乌润，香气高雅，滋味醇厚甘甜，享有盛誉。', 280.00, 500, 76, 100, 12, '/images/宁红功夫.jpg');
INSERT INTO `product` VALUES (18, 2, '宜红功夫', '宜红功夫，湖北宜昌特产红茶，外形条索紧细匀齐，色泽乌润。香气清高，滋味鲜爽甘醇，是传统工艺红茶的佳作。', 390.00, 500, 77, 100, 12, '/images/宜红功夫.jpg');
INSERT INTO `product` VALUES (19, 2, '政和工夫', '政和功夫，福建政和特产红茶，外形条索紧结，色泽乌润。香气高雅持久，滋味醇厚甘甜，以其优良品质闻名。', 300.00, 500, 75, 100, 12, '/images/政和工夫.jpg');
INSERT INTO `product` VALUES (20, 2, '遵义红', '遵义红，贵州遵义特产红茶，选用优质鲜叶精制而成。外形条索紧细，色泽乌润，香气馥郁，滋味醇厚甘甜。', 290.00, 500, 600, 100, 12, '/images/遵义红.jpg');
INSERT INTO `product` VALUES (21, 2, '日照红梅', '日照红梅，山东日照特产红茶，因其香气似梅花而得名。外形条索紧细，色泽乌润，香气高雅，滋味鲜爽甘甜。', 300.00, 500, 800, 100, 12, '/images/日照红梅.jpg');
INSERT INTO `product` VALUES (22, 3, '白毫银针', '白毫银针，产自福建福鼎等地，为白茶中的极品。以单芽制成，外形挺直如针，满披白毫，汤色浅黄明亮，滋味清淡鲜爽。', 70.00, 250, 900, 100, 12, '/images/白毫银针.jpg');
INSERT INTO `product` VALUES (23, 3, '老白茶', '老白茶，经多年存放转化而成，具有独特陈香。茶叶色泽深沉，汤色橙红透亮，滋味醇厚甘甜，有显著药用价值。', 90.00, 250, 600, 100, 12, '/images/老白茶.jpg');
INSERT INTO `product` VALUES (24, 3, '福鼎白茶', '福鼎白茶，源自福建福鼎，传统工艺制作。茶叶自然萎凋干燥，不炒不揉，保持天然清香，口感柔和鲜美，回味悠长。', 78.00, 250, 400, 100, 12, '/images/福鼎白茶.jpg');
INSERT INTO `product` VALUES (25, 3, '白牡丹', '白牡丹，由一芽两叶制成的白茶，品质介于白毫银针和寿眉之间。外形绿叶夹银白色毫心，形似花朵，汤色杏黄清澈，香气清幽。', 67.00, 250, 4555, 100, 12, '/images/白牡丹.jpg');
INSERT INTO `product` VALUES (26, 3, '政和白茶', '政和白茶，产于福建政和县，历史悠久。采用当地大白茶品种原料，通过传统工艺制作，汤色金黄明亮，香气清雅，滋味鲜醇。', 78.00, 250, 300, 100, 12, '/images/政和白茶.jpg');
INSERT INTO `product` VALUES (27, 3, '曼谷白茶', '景谷白茶，云南特产，选用景谷大叶种鲜叶精制而成。外形肥壮多毫，汤色橙黄明亮，滋味浓郁甘醇，带有特殊花香气息。', 67.00, 250, 300, 100, 12, '/images/曼谷白茶.jpg');
INSERT INTO `product` VALUES (28, 3, '贡眉', '贡眉，白茶的一种，主要由叶片制成。外形自然舒展，色泽灰绿带褐，汤色橙黄，香气清新，滋味醇厚且耐泡。', 76.00, 250, 200, 100, 12, '/images/贡眉.jpg');
INSERT INTO `product` VALUES (29, 3, '寿眉', '寿眉，是白茶中产量最大的品类，采摘标准较为宽松。茶叶形状自然，色泽暗绿，汤色橙黄，味道醇厚回甘，适合长期保存', 56.00, 250, 100, 100, 12, '/images/寿眉.jpg');
INSERT INTO `product` VALUES (30, 4, '茉莉花茶', '茉莉花茶，绿茶与茉莉鲜花结合窨制而成。茶叶吸收了茉莉花香，香气浓郁持久，汤色黄绿明亮，口感清爽甜美。', 57.00, 100, 300, 100, 12, '/images/茉莉花茶.jpg');
INSERT INTO `product` VALUES (31, 4, '白兰花茶', '白兰花茶，将白兰花与茶叶混合窨制，使茶叶充分吸收花香。香气馥郁芬芳，滋味鲜爽甘甜，具有独特的白兰香气。', 74.00, 100, 900, 100, 12, '/images/白兰花茶.jpg');
INSERT INTO `product` VALUES (32, 4, '珠兰花茶', '珠兰花茶，利用珠兰花熏制茶叶而成。香气独特优雅，汤色清澈明亮，滋味鲜爽回甘，带有淡淡的珠兰花香。', 50.00, 100, 800, 100, 12, '/images/珠兰花茶.jpg');
INSERT INTO `product` VALUES (33, 4, '菊花茶', '菊花茶，采用菊花干品冲泡饮用，不添加茶叶。具有清热解毒、明目降压的功效，汤色金黄，香气淡雅，口味清凉。', 97.00, 100, 800, 100, 12, '/images/菊花茶.jpg');
INSERT INTO `product` VALUES (34, 4, '玫瑰花茶', '玫瑰花茶，使用新鲜或干燥的玫瑰花瓣制成。香气芬芳浓郁，汤色红艳美丽，具有美容养颜、舒缓情绪的效果。', 67.00, 100, 800, 100, 12, '/images/玫瑰花茶.jpg');
INSERT INTO `product` VALUES (35, 4, '桂花茶', '桂花茶，将桂花与茶叶混合制作而成。香气浓郁甜润，汤色金黄，滋味甘醇，常饮有助于改善口气和提神醒脑。', 89.00, 100, 800, 100, 12, '/images/桂花茶.jpg');
INSERT INTO `product` VALUES (36, 4, '桃花茶', '桃花茶，采用新鲜桃花或干燥桃花泡制。汤色淡粉美观，具有活血化瘀、美容养颜等功效，香气淡雅宜人。', 78.00, 100, 900, 100, 12, '/images/桃花茶.jpg');
INSERT INTO `product` VALUES (37, 4, '金银花茶', '金银花茶，由金银花干品泡制，具有清热解毒、抗炎消肿的功效。汤色淡黄，香气清新，味道微苦后甜。', 76.00, 100, 90, 100, 12, '/images/金银花茶.jpg');
INSERT INTO `product` VALUES (38, 4, '百合花茶', '百合花茶，选用百合花干燥后泡制而成。香气淡雅清新，汤色透明微黄，具有润肺止咳、安神助眠的作用。', 90.00, 100, 900, 100, 12, '/images/百合花茶.jpg');

-- ----------------------------
-- Table structure for review
-- ----------------------------
DROP TABLE IF EXISTS `review`;
CREATE TABLE `review`  (
  `review_id` bigint NOT NULL AUTO_INCREMENT COMMENT '评价ID',
  `order_id` bigint NULL DEFAULT NULL COMMENT '订单ID(可为空，表示非订单评论)',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `rating` tinyint NOT NULL COMMENT '评分(1-5)',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '评价内容',
  `reply` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '商家回复',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`review_id`) USING BTREE,
  INDEX `idx_order_id`(`order_id` ASC) USING BTREE,
  INDEX `idx_product_id`(`product_id` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  CONSTRAINT `fk_review_order` FOREIGN KEY (`order_id`) REFERENCES `order` (`order_id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_review_product` FOREIGN KEY (`product_id`) REFERENCES `product` (`product_id`) ON DELETE CASCADE ON UPDATE RESTRICT,
  CONSTRAINT `fk_review_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '评价表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of review
-- ----------------------------

-- ----------------------------
-- Table structure for user
-- ----------------------------
DROP TABLE IF EXISTS `user`;
CREATE TABLE `user`  (
  `user_id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码(加密存储)',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '电子邮箱',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '手机号',
  `real_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '真实姓名',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '头像URL',
  `gender` tinyint NULL DEFAULT 0 COMMENT '性别(0-未知 1-男 2-女)',
  `birth_date` date NULL DEFAULT NULL COMMENT '出生日期',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '默认地址',
  PRIMARY KEY (`user_id`) USING BTREE,
  UNIQUE INDEX `idx_username`(`username` ASC) USING BTREE,
  INDEX `idx_phone`(`phone` ASC) USING BTREE,
  INDEX `idx_email`(`email` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of user
-- ----------------------------
INSERT INTO `user` VALUES (1, 'alice123', '123456', '<EMAIL>', '1234567890', 'Alice Smith', 'http://example.com/avatar/alice.jpg', 2, '1990-01-01', '123 Green St, Cityville');
INSERT INTO `user` VALUES (2, 'bob456', '123456', '<EMAIL>', '0987654321', 'Bob Johnson', 'http://example.com/avatar/bob.jpg', 1, '1985-05-05', '456 Blue Ave, Townsville');
INSERT INTO `user` VALUES (6, 'aaasen', '123456', '<EMAIL>', '0987090899', 'Bob Johnson', 'http://example.com/avatar/bob.jpg', 1, '1985-05-09', '456 Blue Ave, Townsville');

SET FOREIGN_KEY_CHECKS = 1;
