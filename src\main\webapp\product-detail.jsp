<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<html>
<head>
    <title>商品详情 - 茶品商城</title>
    <link rel="stylesheet" href="css/style.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container">
        <h1>${product.name} 详情</h1>
        <div class="product-detail">
            <img src="${product.mainImage}" alt="${product.name}" class="detail-image">
            <div class="detail-info">
                <p>分类：${product.categoryName}</p>
                <p>描述：${product.description}</p>
                <p>规格：${product.weight}g</p>
                <p class="price">价格：￥${product.price}</p>
                <form action="/cart" method="post">
                    <input type="hidden" name="action" value="add">
                    <input type="hidden" name="productId" value="${product.id}">
                    <input type="number" name="quantity" value="1" min="1" class="quantity-input">
                    <button type="submit" class="btn">加入购物车</button>
                </form>
                <form action="/inorder" method="get">
                    <input type="hidden" name="action" value="directBuy">
                    <input type="hidden" name="productId" value="${product.id}">
                    <input type="number" name="quantity" value="1" min="1" class="quantity-input">
                    <button type="submit" class="btn">直接购买</button>
                </form>
            </div>
        </div>
        
        <div class="comments-section">
            <h2>用户评论</h2>
            
            <!-- 错误消息显示 -->
            <c:if test="${not empty param.error}">
                <div class="error-message">${param.error}</div>
            </c:if>
            
            <!-- 评论列表 -->
            <div class="comments-list">
                <c:if test="${empty comments}">
                    <p>暂无评论，快来发表第一条评论吧！</p>
                </c:if>
                <c:forEach items="${comments}" var="comment">
                    <div class="comment-item">
                        <div class="comment-header">
                            <span class="comment-username">${comment.username}</span>
                            <span class="comment-time">${comment.createTime}</span>
                        </div>
                        <div class="comment-content">${comment.content}</div>
                    </div>
                </c:forEach>
            </div>
            
            <!-- 评论表单 -->
            <c:if test="${not empty sessionScope.username}">
                <div class="comment-form-section">
                    <h3>发表评论</h3>
                    <form action="/comment" method="post" class="comment-form">
                        <input type="hidden" name="productId" value="${product.id}">
                        <textarea name="content" rows="4" cols="50" placeholder="请输入您的评论..." required></textarea>
                        <br>
                        <button type="submit" class="btn">提交评论</button>
                    </form>
                </div>
            </c:if>
            <c:if test="${empty sessionScope.username}">
                <p class="login-prompt">请<a href="/login.jsp">登录</a>后发表评论</p>
            </c:if>
        </div>
        
        <a href="/" class="back-link">返回首页</a>
    </div>
</body>
</html>