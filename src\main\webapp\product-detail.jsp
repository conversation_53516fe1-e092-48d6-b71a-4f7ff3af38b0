<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品详情 - 茶品商城</title>
    <link rel="stylesheet" href="css/style.css">
    <style>
        .comments-section {
            margin-top: 30px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 8px;
        }
        .comments-section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .comment-item {
            background: white;
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .comment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding-bottom: 8px;
            border-bottom: 1px solid #eee;
        }
        .comment-username {
            font-weight: bold;
            color: #2c3e50;
        }
        .comment-rating {
            color: #f39c12;
            font-size: 14px;
        }
        .comment-time {
            color: #7f8c8d;
            font-size: 12px;
        }
        .comment-content {
            color: #333;
            line-height: 1.6;
        }
        .comment-form-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .comment-form-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        .rating-section {
            margin-bottom: 15px;
        }
        .rating-section label {
            font-weight: bold;
            margin-right: 10px;
        }
        .rating-section select {
            padding: 5px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .comment-form textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            resize: vertical;
            font-family: inherit;
        }
        .error-message {
            background-color: #fadbd8;
            color: #e74c3c;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        .success-message {
            background-color: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
            border: 1px solid #c3e6cb;
        }
        .login-prompt {
            text-align: center;
            padding: 20px;
            background: white;
            border-radius: 8px;
            margin-top: 20px;
        }
        .login-prompt a {
            color: #3498db;
            text-decoration: none;
        }
        .login-prompt a:hover {
            text-decoration: underline;
        }
    </style>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container">
        <h1>${product.name} 详情</h1>
        <div class="product-detail">
            <img src="${product.mainImage}" alt="${product.name}" class="detail-image">
            <div class="detail-info">
                <p>分类：${product.categoryName}</p>
                <p>描述：${product.description}</p>
                <p>规格：${product.weight}g</p>
                <p class="price">价格：￥${product.price}</p>
                <form action="/cart" method="post">
                    <input type="hidden" name="action" value="add">
                    <input type="hidden" name="productId" value="${product.id}">
                    <input type="number" name="quantity" value="1" min="1" class="quantity-input">
                    <button type="submit" class="btn">加入购物车</button>
                </form>
                <form action="/inorder" method="get">
                    <input type="hidden" name="action" value="directBuy">
                    <input type="hidden" name="productId" value="${product.id}">
                    <input type="number" name="quantity" value="1" min="1" class="quantity-input">
                    <button type="submit" class="btn">直接购买</button>
                </form>
            </div>
        </div>
        
        <div class="comments-section">
            <h2>用户评论</h2>
            
            <!-- 消息显示 -->
            <c:if test="${not empty param.error}">
                <div class="error-message">${param.error}</div>
            </c:if>
            <c:if test="${not empty param.success}">
                <div class="success-message">${param.success}</div>
            </c:if>
            
            <!-- 评论列表 -->
            <div class="comments-list">
                <c:if test="${empty comments}">
                    <p>暂无评论，快来发表第一条评论吧！</p>
                </c:if>
                <c:forEach items="${comments}" var="comment">
                    <div class="comment-item">
                        <div class="comment-header">
                            <span class="comment-username">${comment.username}</span>
                            <span class="comment-rating">
                                <c:forEach begin="1" end="${comment.rating}">⭐</c:forEach>
                                (${comment.rating}/5)
                            </span>
                            <span class="comment-time">${comment.createTime}</span>
                        </div>
                        <div class="comment-content">${comment.content}</div>
                    </div>
                </c:forEach>
            </div>
            
            <!-- 评论表单 -->
            <c:if test="${not empty sessionScope.user}">
                <div class="comment-form-section">
                    <h3>发表评论</h3>
                    <form action="/comment" method="post" class="comment-form">
                        <input type="hidden" name="productId" value="${product.id}">
                        <div class="rating-section">
                            <label>评分：</label>
                            <select name="rating" required>
                                <option value="">请选择评分</option>
                                <option value="5">⭐⭐⭐⭐⭐ 非常满意</option>
                                <option value="4">⭐⭐⭐⭐ 满意</option>
                                <option value="3">⭐⭐⭐ 一般</option>
                                <option value="2">⭐⭐ 不满意</option>
                                <option value="1">⭐ 非常不满意</option>
                            </select>
                        </div>
                        <textarea name="content" rows="4" cols="50" placeholder="请输入您的评论..." required></textarea>
                        <br>
                        <button type="submit" class="btn">提交评论</button>
                    </form>
                </div>
            </c:if>
            <c:if test="${empty sessionScope.user}">
                <p class="login-prompt">请<a href="/login.jsp">登录</a>后发表评论</p>
            </c:if>
        </div>
        
        <a href="/" class="back-link">返回首页</a>
    </div>
</body>
</html>