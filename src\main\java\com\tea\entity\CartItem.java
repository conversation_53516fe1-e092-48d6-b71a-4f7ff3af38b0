package com.tea.entity;

public class CartItem {
    private Long productId;
    private Integer quantity;
    private String name;
    private Double price;
    private String mainImage;

    public CartItem() {
    }

    public CartItem(Long productId, Integer quantity, String name, Double price, String mainImage) {
        this.productId = productId;
        this.quantity = quantity;
        this.name = name;
        this.price = price;
        this.mainImage = mainImage;
    }

    // Getters and Setters
    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public String getMainImage() {
        return mainImage;
    }

    public void setMainImage(String mainImage) {
        this.mainImage = mainImage;
    }
}