package com.tea.dao;

import com.tea.util.DBUtil;
import com.tea.entity.CartItem;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CartDao {
    /**
     * 添加商品到购物车
     */
    public void addCartItem(Long userId, Long productId, Integer quantity, boolean selected) throws Exception {
        String sql = "INSERT INTO cart (user_id, product_id, quantity, selected) VALUES (?, ?, ?, ?)";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setLong(1, userId);
            pstmt.setLong(2, productId);
            pstmt.setInt(3, quantity);
            pstmt.setBoolean(4, selected);
            pstmt.executeUpdate();
        }
    }

    /**
     * 更新购物车商品数量
     */
    public void updateCartItem(Long userId, Long productId, Integer quantity) throws Exception {
        String sql = "UPDATE cart SET quantity = ? WHERE user_id = ? AND product_id = ?";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setInt(1, quantity);
            pstmt.setLong(2, userId);
            pstmt.setLong(3, productId);
            pstmt.executeUpdate();
        }
    }

    /**
     * 从购物车移除商品
     */
    public void removeCartItem(Long userId, Long productId) throws Exception {
        String sql = "DELETE FROM cart WHERE user_id = ? AND product_id = ?";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setLong(1, userId);
            pstmt.setLong(2, productId);
            pstmt.executeUpdate();
        }
    }

    /**
     * 获取用户购物车商品列表
     */
    public List<CartItem> getUserCartItems(Long userId) throws Exception {
        List<CartItem> cartItems = new ArrayList<>();
        String sql = "SELECT c.product_id, c.quantity, p.name, p.price, p.main_image " +
                     "FROM cart c JOIN product p ON c.product_id = p.product_id WHERE c.user_id = ?";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setLong(1, userId);
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    CartItem item = new CartItem();
                    item.setProductId(rs.getLong("product_id"));
                    item.setQuantity(rs.getInt("quantity"));
                    item.setName(rs.getString("name"));
                    item.setPrice(rs.getDouble("price"));
                    item.setMainImage(rs.getString("main_image"));
                    cartItems.add(item);
                }
            }
        }
        return cartItems;
    }

    /**
     * 获取购物车中商品数量
     */
    public int getCartItemQuantity(Long userId, Long productId) throws Exception {
        String sql = "SELECT quantity FROM cart WHERE user_id = ? AND product_id = ?";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setLong(1, userId);
            pstmt.setLong(2, productId);
            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getInt("quantity");
                }
            }
        }
        return 0;
    }

    /**
     * 清空购物车
     */
    public void clearCart(Long userId) throws Exception {
        String sql = "DELETE FROM cart WHERE user_id = ?";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setLong(1, userId);
            pstmt.executeUpdate();
        }
    }
}