<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单详情 - 茶品商城</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        body {
            background-color: #f8f9fa;
            color: #333;
            line-height: 1.6;
            padding: 20px;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            overflow: hidden;
        }
        .header {
            background-color: #2c3e50;
            color: white;
            padding: 20px 30px;
            border-bottom: 1px solid #eee;
        }
        .header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }
        .header p {
            opacity: 0.9;
            font-size: 14px;
        }
        .order-info {
            padding: 30px;
            border-bottom: 1px solid #f0f0f0;
        }
        .order-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px dashed #eee;
        }
        .order-id {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
        }
        .order-date {
            color: #7f8c8d;
            font-size: 14px;
        }
        .order-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 14px;
        }
        .status-pending {
            background-color: #f1c40f;
            color: white;
        }
        .status-paid {
            background-color: #2ecc71;
            color: white;
        }
        .status-shipped {
            background-color: #3498db;
            color: white;
        }
        .status-completed {
            background-color: #27ae60;
            color: white;
        }
        .status-cancelled {
            background-color: #e74c3c;
            color: white;
        }
        .product-list {
            padding: 0 30px 30px;
        }
        .product-item {
            display: flex;
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .product-item:last-child {
            border-bottom: none;
        }
        .product-image {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 8px;
            margin-right: 20px;
        }
        .product-info {
            flex: 1;
        }
        .product-name {
            font-weight: 600;
            margin-bottom: 5px;
        }
        .product-price {
            color: #e74c3c;
            font-weight: bold;
        }
        .product-quantity {
            color: #7f8c8d;
        }
        .order-summary {
            padding: 20px 30px;
            background-color: #f9f9f9;
            border-top: 1px solid #f0f0f0;
        }
        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 14px;
        }
        .summary-total {
            font-size: 18px;
            font-weight: bold;
            color: #e74c3c;
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px solid #eee;
        }
        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 30px;
            padding: 20px;
            border-top: 1px solid #f0f0f0;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            text-align: center;
            transition: all 0.3s;
        }
        .btn-primary {
            background-color: #3498db;
            color: white;
        }
        .btn-primary:hover {
            background-color: #2980b9;
        }
        .btn-secondary {
            background-color: #95a5a6;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #7f8c8d;
        }
        .error-message {
            padding: 20px;
            background-color: #fadbd8;
            color: #e74c3c;
            border-radius: 4px;
            margin: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>订单详情</h1>
            <p>查看您的订单信息和状态</p>
        </div>

        <c:choose>
            <c:when test="${empty order}">
                <div class="error-message">
                    订单不存在或已被删除
                </div>
            </c:when>
            <c:otherwise>
                <div class="order-info">
                    <div class="order-header">
                        <div>
                            <span class="order-id">订单编号: ${order.orderId}</span>
                            <span class="order-date">下单时间: ${order.orderDate}</span>
                            <c:choose>
                                <c:when test="${order.status == 0}"><span class="order-status status-pending">待付款</span></c:when>
                                <c:when test="${order.status == 1}"><span class="order-status status-paid">已付款</span></c:when>
                                <c:when test="${order.status == 2}"><span class="order-status status-shipped">已发货</span></c:when>
                                <c:when test="${order.status == 3}"><span class="order-status status-completed">已完成</span></c:when>
                                <c:when test="${order.status == 4}"><span class="order-status status-cancelled">已取消</span></c:when>
                                <c:otherwise><span class="order-status">未知状态</span></c:otherwise>
                            </c:choose>
                        </div>
                    </div>

                    <div class="product-list">
                        <c:forEach var="item" items="${orderItems}">
                            <div class="product-item">
                                <img src="${item.productImage}" alt="${item.productName}" class="product-image">
                                <div class="product-info">
                                    <h3 class="product-name">${item.productName}</h3>
                                    <p>单价: ¥${item.unitPrice}</p>
                                    <p>数量: ${item.quantity}</p>
                                    <p>小计: ¥${item.price}</p>
                                </div>
                            </div>
                        </c:forEach>
                    </div>

                    <div class="order-summary">
                        <div class="summary-row">
                            <span>商品总价:</span>
                            <span>¥${order.totalAmount}</span>
                        </div>
                        <div class="summary-row summary-total">
                            <span>订单总金额:</span>
                            <span>¥${order.totalAmount}</span>
                        </div>
                    </div>

                    <div class="action-buttons">
                        <c:if test="${order.status == 0}">
                            <a href="/payment?action=process&orderId=${order.orderId}" class="btn btn-primary">立即支付</a>
                        </c:if>
                        <a href="/order?action=list" class="btn btn-secondary">返回订单列表</a>
                    </div>
                </div>
            </c:otherwise>
        </c:choose>
    </div>
</body>
</html>