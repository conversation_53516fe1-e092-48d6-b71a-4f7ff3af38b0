# Comment Functionality Test Guide

## Prerequisites
1. Execute the database update script: `update_review_table_fix.sql`
2. Restart your application server
3. Ensure you have test user accounts and products in the database

## Test Cases

### Test Case 1: Database Schema Fix
**Objective**: Verify that the order_id foreign key constraint issue is resolved

**Steps**:
1. Connect to your MySQL database
2. Execute: `DESCRIBE review;`
3. Verify that `order_id` column shows `NULL` as allowed
4. Execute: `SHOW CREATE TABLE review;`
5. Verify that the foreign key constraint allows NULL values

**Expected Result**: 
- `order_id` column should be nullable
- Foreign key constraint should be `ON DELETE SET NULL`

### Test Case 2: Comment Submission Flow
**Objective**: Test the complete comment submission and redirect flow

**Steps**:
1. Navigate to a product detail page (e.g., `/product-detail?productId=1`)
2. Ensure you are logged in
3. Scroll down to the comment section
4. Fill in the comment form:
   - Select a rating (1-5 stars)
   - Enter comment content
5. Click "提交评论" (Submit Comment)

**Expected Result**:
- Should redirect back to the same product detail page
- Should show success message: "评论提交成功"
- New comment should appear in the comments list
- Comment should show correct username, rating, and content

### Test Case 3: Duplicate Comment Prevention
**Objective**: Verify that users cannot submit multiple comments for the same product

**Steps**:
1. Complete Test Case 2 successfully
2. Try to submit another comment for the same product
3. Fill in the form and submit

**Expected Result**:
- Should redirect back to product detail page
- Should show error message: "您已经对该商品发表过评论了"
- No new comment should be added

### Test Case 4: Validation Testing
**Objective**: Test form validation

**Steps**:
1. Navigate to product detail page
2. Try to submit comment with empty content
3. Try to submit comment without selecting rating
4. Try to submit comment with invalid product ID

**Expected Results**:
- Empty content: "评论内容不能为空"
- No rating: "请选择评分"
- Invalid product ID: Appropriate error handling

### Test Case 5: Login Requirement
**Objective**: Verify that only logged-in users can submit comments

**Steps**:
1. Log out of the application
2. Navigate to a product detail page
3. Verify comment form is not visible
4. Should see "请登录后发表评论" message

**Expected Result**:
- Comment form should not be visible to non-logged-in users
- Login prompt should be displayed

### Test Case 6: Comment Display
**Objective**: Verify that comments are displayed correctly

**Steps**:
1. Navigate to a product with existing comments
2. Verify comment display includes:
   - Username
   - Star rating (⭐⭐⭐⭐⭐)
   - Numeric rating (X/5)
   - Comment content
   - Creation time

**Expected Result**:
- All comment information should be displayed correctly
- Comments should be ordered by creation time (newest first)

## Troubleshooting

### If you still get foreign key constraint errors:
1. Check if the database update script was executed successfully
2. Verify that there are no existing records with order_id = 0
3. Restart your application server

### If comments don't appear after submission:
1. Check server logs for any exceptions
2. Verify that the ReviewDao.getReviewsByProductId() method is working
3. Check if the ProductDetailServlet is correctly setting the comments attribute

### If redirect doesn't work:
1. Check that the CommentServlet is correctly handling the productId parameter
2. Verify that the redirect URL is properly formed
3. Check for any exceptions in the server logs

## Database Queries for Debugging

```sql
-- Check review table structure
DESCRIBE review;

-- Check existing reviews
SELECT * FROM review ORDER BY create_time DESC LIMIT 10;

-- Check for reviews with NULL order_id
SELECT * FROM review WHERE order_id IS NULL;

-- Check foreign key constraints
SELECT 
    CONSTRAINT_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM information_schema.KEY_COLUMN_USAGE 
WHERE TABLE_NAME = 'review' AND TABLE_SCHEMA = 'teashop';
```
