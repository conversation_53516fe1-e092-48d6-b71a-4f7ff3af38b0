<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付成功 - 茶品商城</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        body {
            background-color: #f8f9fa;
            color: #333;
            line-height: 1.6;
            padding: 40px 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.05);
            overflow: hidden;
        }
        .header {
            background-color: #2ecc71;
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }
        .header p {
            opacity: 0.9;
        }
        .success-icon {
            width: 80px;
            height: 80px;
            background-color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            box-shadow: 0 4px 12px rgba(46,204,113,0.2);
        }
        .success-icon::after {
            content: '✓';
            font-size: 40px;
            color: #2ecc71;
            font-weight: bold;
        }
        .content {
            padding: 30px;
        }
        .order-info {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .order-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #555;
        }
        .order-details {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px dashed #eee;
        }
        .order-details:last-child {
            border-bottom: none;
        }
        .label {
            color: #777;
        }
        .value {
            font-weight: bold;
        }
        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 30px;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            text-align: center;
            transition: all 0.3s;
        }
        .btn-primary {
            background-color: #3498db;
            color: white;
        }
        .btn-primary:hover {
            background-color: #2980b9;
        }
        .btn-secondary {
            background-color: #95a5a6;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #7f8c8d;
        }
        .tips {
            margin-top: 20px;
            text-align: center;
            color: #7f8c8d;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="success-icon"></div>
            <h1>支付成功！</h1>
            <p>您的订单已支付完成，我们将尽快为您发货</p>
        </div>

        <div class="content">
            <div class="order-info">
                <h3 class="order-title">订单信息</h3>
                <div class="order-details">
                    <div class="order-details-item">
                        <span class="label">订单编号:</span>
                        <span class="value">${order.orderId}</span>
                    </div>
                    <div class="order-details-item">
                        <span class="label">支付金额:</span>
                        <span class="value">¥${order.totalAmount}</span>
                    </div>
                    <div class="order-details-item">
                        <span class="label">支付时间:</span>
                        <span class="value">${paymentTime}</span>
                    </div>
                    <div class="order-details-item">
                        <span class="label">订单状态:</span>
                        <span class="value">已支付 - 待发货</span>
                    </div>
                </div>
            </div>

            <div class="action-buttons">
                <a href="/order?action=view&orderId=${order.orderId}" class="btn btn-primary">查看订单详情</a>
                <a href="/index.jsp" class="btn btn-secondary">返回首页</a>
            </div>

            <div class="tips">
                <p>如有任何疑问，请联系客服: 400-123-4567 | 工作时间: 9:00-21:00</p>
            </div>
        </div>
    </div>
</body>
</html>