<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付失败 - 茶品商城</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        body {
            background-color: #f8f9fa;
            color: #333;
            line-height: 1.6;
            padding: 40px 20px;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.05);
            overflow: hidden;
        }
        .header {
            background-color: #e74c3c;
            color: white;
            padding: 30px;
            text-align: center;
        }
        .error-icon {
            width: 80px;
            height: 80px;
            background-color: white;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .error-icon::after {
            content: '✗';
            font-size: 40px;
            color: #e74c3c;
            font-weight: bold;
        }
        .header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }
        .content {
            padding: 30px;
        }
        .error-message {
            background-color: #fadbd8;
            border-left: 4px solid #e74c3c;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
            font-weight: bold;
        }
        .suggestions {
            margin: 20px 0;
        }
        .suggestion-item {
            display: flex;
            margin-bottom: 10px;
            align-items: flex-start;
        }
        .suggestion-item::before {
            content: '•';
            color: #e74c3c;
            font-weight: bold;
            margin-right: 10px;
            font-size: 18px;
        }
        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 30px;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            text-align: center;
            transition: all 0.3s;
        }
        .btn-primary {
            background-color: #3498db;
            color: white;
        }
        .btn-primary:hover {
            background-color: #2980b9;
        }
        .btn-secondary {
            background-color: #95a5a6;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #7f8c8d;
        }
        .tips {
            margin-top: 20px;
            font-size: 14px;
            color: #7f8c8d;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="error-icon"></div>
            <h1>支付失败</h1>
            <p>很抱歉，您的支付未能完成</p>
        </div>

        <div class="content">
            <div class="error-message">
                <c:choose>
                    <c:when test="${not empty errorMsg}">${errorMsg}</c:when>
                    <c:otherwise>支付处理过程中出现未知错误，请稍后重试</c:otherwise>
                </c:choose>
            </div>

            <h3>可能的原因：</h3>
            <div class="suggestions">
                <div class="suggestion-item">账户余额不足或支付方式受限</div>
                <div class="suggestion-item">网络连接不稳定导致支付请求未送达</div>
                <div class="suggestion-item">支付系统暂时维护或升级中</div>
                <div class="suggestion-item">银行卡信息填写错误或已过期</div>
            </div>

            <div class="action-buttons">
                <a href="javascript:history.back()" class="btn btn-primary">返回重试</a>
                <a href="/cart.jsp" class="btn btn-secondary">返回购物车</a>
            </div>

            <div class="tips">
                <p>如果问题持续存在，请联系客服: 400-123-4567 (工作日 9:00-21:00)</p>
            </div>
        </div>
    </div>
</body>
</html>