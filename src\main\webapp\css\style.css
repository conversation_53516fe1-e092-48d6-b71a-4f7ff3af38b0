* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}
/* 全局去掉a标签下划线，结合你场景，可放到之前的CSS里合适位置 */
a {
    text-decoration: none;
}

:root {
    --primary-color: #60a164;
    --secondary-color: #F0F8F1;
    --text-dark: #333;
    --text-light: #666;
    --border-radius: 12px;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #91c0ab;
    color: var(--text-dark);
    line-height: 1.6;
}

.header {
    background: linear-gradient(135deg, var(--primary-color) 0%, #256529 100%);
    padding: 1.5rem 2rem;
    color: white;
    margin-bottom: 2.5rem;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    position: relative;
}

.header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: rgba(255,255,255,0.15);
}

.container {
    max-width: 100%;
    margin: 0 auto;
    padding: 0 2rem;
}

.category-grid {
    display: flex;
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap;
    gap: 20px;
    margin: 30px 0;
    padding: 10px 0;
    width: 100%;
}

.category-card {
    background-image: url('/images/category-bg.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border-radius: var(--border-radius);
    width: 100%;
    overflow: hidden;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0,0,0,0.06);
    transition: all 0.3s ease;
    position: relative;
    flex: 1;
    min-width: 400px;
    max-width: calc(33.333% - 20px);
    min-height: 750px;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background-color: #f0f0f0;
}

.category-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.15);
}

.category-link {
    text-decoration: none;
    color: var(--text-dark);
    display: block;
}

.product-tab-link, .category-card h3 {
  text-decoration: none;
}

.product-img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    display: block;
    border: 1px solid transparent; /* 确保元素占位 */
    background-color: #f5f5f5; /* 加载前背景色 */
}

.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    padding: 20px;
}

.category-image {
    height: 500px;
    background-size: cover;
    background-position: center;
    border-bottom: 4px solid var(--primary-color);
}

.category-name {
    font-size: 1.4rem;
    font-weight: 600;
    margin: 1rem 1.5rem 0.5rem;
}

.category-desc {
    font-size: 1rem;
    color: var(--text-light);
    margin: 0 1.5rem 1.5rem;
    line-height: 1.4;
}

.category-btn {
    padding: 0.6rem 1.2rem;
    border: 2px solid var(--primary-color);
    border-radius: var(--border-radius);
    background-image: url('/images/category-bg.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    color: var(--primary-color);
    font-size: 1rem;
    text-decoration: none;
    cursor: pointer;
    /* 确保所有链接状态无下划线 */
    &:link, &:visited, &:hover, &:active {
      text-decoration: none;
    }
    transition: all 0.3s ease;
}

.category-btn:hover {
    background: var(--primary-color);
    color: white;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.category-btn.active {
    background: var(--primary-color);
    color: white;
}

.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 2.5rem;
    padding: 1.5rem;
}

.product-card {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 6px 18px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    position: relative;
    text-decoration: none;
    background-color: white; /* 添加白色背景确保内容可见 */
    
}
.product-info{
    background-color: #f0f0f0;
}

.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-color);
    opacity: 0;
    transition: opacity 0.3s;
}



.product-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 24px rgba(0,0,0,0.12);
}

.header {
    background: linear-gradient(135deg, var(--primary-color) 0%, #256529 100%);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    padding: 1rem 0;
    position: relative;
}

.header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: rgba(255,255,255,0.15);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 1.8rem;
    font-weight: 700;
    color: white;
}

.nav-links {
    display: flex;
    gap: 2rem;
}

.nav-links a {
    color: rgba(255,255,255,0.9);
    text-decoration: none;
    font-size: 1.1rem;
    transition: color 0.3s;
}

.nav-links a:hover {
    color: white;
    text-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.auth-user-container {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    color: white;
}

.user-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid rgba(255,255,255,0.2);
}

.user-name {
    font-size: 1rem;
    font-weight: 500;
    color: rgba(255,255,255,0.9);
}

.auth-link {
    color: rgba(255,255,255,0.7);
    text-decoration: none;
    font-size: 1rem;
    transition: color 0.3s;
}

.auth-link:hover {
    color: white;
}

.auth-container {
    max-width: 480px;
    margin: 0 auto;
    background-image: url('/images/category-bg.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border-radius: var(--border-radius);
    padding: 2.5rem 3rem;
    box-shadow: 0 6px 18px rgba(0,0,0,0.08); /* 与product-card阴影一致 */
    transition: all 0.3s ease; /* 新增过渡效果 */
}

.auth-container:hover {
    transform: translateY(-4px); /* 与product-card悬停位移一致 */
    box-shadow: 0 8px 24px rgba(0,0,0,0.12); /* 与product-card悬停阴影一致 */
}

.auth-title {
    color: var(--primary-color);
    font-size: 1.8rem;
    margin-bottom: 2rem;
    text-align: center;
    font-weight: 600; /* 与product-name字重一致 */
    line-height: 1.2; /* 与product-name行高一致 */
}

.auth-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.auth-input {
    padding: 0.8rem 1.2rem;
    border: 1px solid #e0e0e0;
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: border-color 0.3s, box-shadow 0.3s; /* 与product-img过渡属性一致 */
}

.auth-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(46,125,50,0.15);
}

.auth-btn {
    background: var(--primary-color);
    color: white;
    padding: 0.8rem 1.2rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s, transform 0.3s; /* 与nav-links a过渡属性一致 */
}

.auth-btn:hover {
    background: #256529;
    transform: translateY(-2px); /* 新增悬停位移效果 */
}

.auth-btn:hover {
    background: #256529;
}

.auth-helper {
    text-align: center;
    color: var(--text-light);
    margin-top: 1rem;
}

.auth-helper a {
    color: var(--primary-color);
    text-decoration: none;
}

.auth-helper a:hover {
    text-decoration: underline;
}

.product-card:hover::before {
    opacity: 1;
}

.product-img {
    width: 100%;
    height: 240px;
    object-fit: cover;
    object-position: center;
    filter: brightness(0.98);
    transition: filter 0.3s;
}

.product-card:hover .product-img {
    filter: brightness(1);
}

.product-info {
    padding: 2rem 1.8rem;
}

.product-name {
    color: var(--primary-color);
    margin-bottom: 0.8rem;
    font-size: 1.3rem;
    font-weight: 600;
    line-height: 1.2;
}

.product-desc {
    color: var(--text-light);
    font-size: 0.95rem;
    margin-bottom: 1.2rem;
    line-height: 1.5;
    min-height: 60px;
}

.product-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.product-price::before {
    content: '￥';
    font-size: 1.2rem;
    font-weight: 500;
}

.cart-table, .order-table {
    width: 100%;
    border-collapse: collapse;
    margin: 2rem 0;
    background-image: url('/images/category-bg.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
}

.cart-table th, .order-table th {
    background: var(--secondary-color);
    padding: 1.2rem;
    text-align: left;
    color: var(--primary-color);
    font-weight: 600;
}

.cart-table td, .order-table td {
    padding: 1.2rem;
    border-bottom: 1px solid #f0f0f0;
}

.cart-table tr:last-child td, .order-table tr:last-child td {
    border-bottom: none;
}

.cart-image, .order-image {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 8px;
    margin-right: 1rem;
}

.product-cell {
    display: flex;
    align-items: center;
}

.quantity-input {
    padding: 0.5rem 0.8rem;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    font-size: 0.95rem;
    width: 80px;
    transition: border-color 0.3s;
}

.quantity-input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.btn {
    padding: 0.8rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 0.95rem;
    cursor: pointer;
    transition: all 0.3s;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    position: relative;
    overflow: hidden;
}

@keyframes processing {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.processing-animation {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: processing 1s ease-in-out infinite;
    margin-right: 10px;
    vertical-align: middle;
}

.btn-primary:hover {
    background: #256529;
    transform: translateY(-1px);
}

.btn-small {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #bb2d3b;
}

.empty-cart {
    padding: 2rem;
    text-align: center;
    color: var(--text-light);
    font-size: 1.1rem;
}

.total-section {
    margin: 2rem 0;
    padding: 1.5rem;
    background: var(--secondary-color);
    border-radius: var(--border-radius);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.total-price {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--primary-color);
}

.product-detail {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    margin: 3rem 0;
    background-image: url('/images/category-bg.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    padding: 2.5rem;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
}

.detail-image {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.detail-info {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.detail-info p {
    font-size: 1.05rem;
    color: var(--text-light);
}

.detail-info .price {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-color);
}

.back-link {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 1rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 2rem;
}

.back-link:hover {
    text-decoration: underline;
}
