<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的订单 - 茶品商城</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        body {
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e0e0e0;
        }
        .header h1 {
            color: #2c3e50;
            font-size: 24px;
        }
        .back-btn {
            padding: 8px 16px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .back-btn:hover {
            background-color: #2980b9;
        }
        .order-list {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .order-item {
            border-bottom: 1px solid #f0f0f0;
            padding: 15px 20px;
            transition: background-color 0.3s;
        }
        .order-item:last-child {
            border-bottom: none;
        }
        .order-item:hover {
            background-color: #f9f9f9;
        }
        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .order-id {
            font-weight: bold;
            color: #2c3e50;
        }
        .order-date {
            color: #7f8c8d;
            font-size: 14px;
        }
        .order-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        .status-pending {
            background-color: #f1c40f;
            color: white;
        }
        .status-paid {
            background-color: #2ecc71;
            color: white;
        }
        .status-shipped {
            background-color: #3498db;
            color: white;
        }
        .status-completed {
            background-color: #27ae60;
            color: white;
        }
        .status-cancelled {
            background-color: #e74c3c;
            color: white;
        }
        .order-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .order-amount {
            font-size: 16px;
            font-weight: bold;
        }
        .order-actions {
            display: flex;
            gap: 10px;
        }
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        .btn-view {
            background-color: #3498db;
            color: white;
        }
        .btn-pay {
            background-color: #2ecc71;
            color: white;
        }
        .btn-cancel {
            background-color: #e74c3c;
            color: white;
        }
        .btn:hover {
            opacity: 0.9;
            transform: translateY(-2px);
        }
        .empty-state {
            text-align: center;
            padding: 50px 20px;
            color: #7f8c8d;
        }
        .empty-state i {
            font-size: 48px;
            margin-bottom: 15px;
            color: #bdc3c7;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>我的订单</h1>
            <a href="/cart" class="back-btn">返回购物车</a>
        </div>

        <div class="order-list">
            <c:choose>
                <c:when test="${empty orders}">
                    <div class="empty-state">
                        <i>📦</i>
                        <h3>您暂无订单记录</h3>
                        <p>快去选购心仪的商品吧！</p>
                        <a href="/index.jsp" class="btn btn-view">去首页购物</a>
                    </div>
                </c:when>
                <c:otherwise>
                    <c:forEach var="order" items="${orders}">
                        <div class="order-item">
                            <div class="order-header">
                                <div>
                                    <span class="order-id">订单号: ${order.orderId}</span>
                                    <span class="order-date">下单时间: ${order.orderDate}</span>
                                    <c:choose>
                                        <c:when test="${order.status == 0}"><span class="order-status status-pending">待付款</span></c:when>
                                        <c:when test="${order.status == 1}"><span class="order-status status-paid">已付款</span></c:when>
                                        <c:when test="${order.status == 2}"><span class="order-status status-shipped">已发货</span></c:when>
                                        <c:when test="${order.status == 3}"><span class="order-status status-completed">已完成</span></c:when>
                                        <c:when test="${order.status == 4}"><span class="order-status status-cancelled">已取消</span></c:when>
                                        <c:otherwise><span class="order-status">未知状态</span></c:otherwise>
                                    </c:choose>
                                </div>
                            </div>
                            <div class="order-details">
                                <div class="order-amount">总金额: ¥${order.totalAmount}</div>
                                <div class="order-actions">
                                    <a href="/order?action=view&orderId=${order.orderId}" class="btn btn-view">查看详情</a>
                                    <c:if test="${order.status == 0}">
                                        <a href="/payment?action=process&orderId=${order.orderId}" class="btn btn-pay">立即支付</a>
                                    </c:if>
                                </div>
                            </div>
                        </div>
                    </c:forEach>
                </c:otherwise>
            </c:choose>
        </div>
    </div>
</body>
</html>