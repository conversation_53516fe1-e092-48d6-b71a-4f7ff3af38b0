package com.tea.dao;

import com.tea.entity.OrderItem;
import com.tea.entity.OrderItemDetail;
import com.tea.util.DBUtil;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class OrderItemDao {
    // 添加订单项
    public boolean addOrderItem(OrderItem orderItem) throws SQLException {
        String sql = "INSERT INTO order_item (order_id, product_id, quantity, total_price) VALUES (?, ?, ?, ?)";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setLong(1, orderItem.getOrderId());
            pstmt.setLong(2, orderItem.getProductId());
            pstmt.setInt(3, orderItem.getQuantity());
            pstmt.setDouble(4, orderItem.getPrice());
            return pstmt.executeUpdate() > 0;
        }
    }

    // 添加订单项（带事务支持）
    public boolean addOrderItem(OrderItem orderItem, Connection conn) throws SQLException {
        String sql = "INSERT INTO order_item (order_id, product_id, quantity, total_price) VALUES (?, ?, ?, ?)";
        PreparedStatement pstmt = conn.prepareStatement(sql);
        pstmt.setLong(1, orderItem.getOrderId());
        pstmt.setLong(2, orderItem.getProductId());
        pstmt.setInt(3, orderItem.getQuantity());
        pstmt.setDouble(4, orderItem.getPrice());
        return pstmt.executeUpdate() > 0;
    }

    // 根据订单ID查询订单项列表
    public List<OrderItem> getOrderItemsByOrderId(long orderId) throws SQLException {
        List<OrderItem> orderItems = new ArrayList<>();
        String sql = "SELECT * FROM order_item WHERE order_id = ?";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setLong(1, orderId);
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    OrderItem item = new OrderItem();
                    item.setOrderItemId(rs.getLong("item_id"));
                    item.setOrderId(rs.getLong("order_id"));
                    item.setProductId(rs.getLong("product_id"));
                    item.setQuantity(rs.getInt("quantity"));
                    item.setPrice(rs.getDouble("total_price"));
                    orderItems.add(item);
                }
            }
        }
        return orderItems;
    }

    // 根据订单ID删除订单项
    public boolean deleteOrderItemsByOrderId(long orderId) throws SQLException {
        String sql = "DELETE FROM order_item WHERE order_id = ?";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setLong(1, orderId);
            return pstmt.executeUpdate() > 0;
        }
    }

    // 根据订单ID查询订单项详细信息（包含商品信息）
    public List<OrderItemDetail> getOrderItemDetailsById(long orderId) throws SQLException {
        List<OrderItemDetail> orderItems = new ArrayList<>();
        String sql = "SELECT oi.item_id, oi.order_id, oi.product_id, oi.quantity, oi.total_price, " +
                     "p.name, p.main_image, p.price " +
                     "FROM order_item oi " +
                     "JOIN product p ON oi.product_id = p.product_id " +
                     "WHERE oi.order_id = ?";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setLong(1, orderId);
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    OrderItemDetail item = new OrderItemDetail();
                    item.setOrderItemId(rs.getLong("item_id"));
                    item.setOrderId(rs.getLong("order_id"));
                    item.setProductId(rs.getLong("product_id"));
                    item.setQuantity(rs.getInt("quantity"));
                    item.setPrice(rs.getDouble("total_price"));
                    item.setProductName(rs.getString("name"));
                    item.setProductImage(rs.getString("main_image"));
                    item.setUnitPrice(rs.getDouble("price"));
                    orderItems.add(item);
                }
            }
        }
        return orderItems;
    }
}