package com.tea.service.impl;

import com.tea.dao.CartDao;
import com.tea.dao.ProductDao;
import com.tea.entity.CartItem;
import com.tea.entity.Product;
import com.tea.service.CartService;
import java.util.HashMap;

import java.util.List;
import java.util.Map;


public class CartServiceImpl implements CartService {
    private final CartDao cartDao = new CartDao();
    private final ProductDao productDao = new ProductDao();

    @Override
    public void addToCart(Long userId, Long productId, Integer quantity) throws Exception {
        // 验证商品是否存在
        Product product = productDao.getProductById(productId);
        if (product == null) {
            throw new IllegalArgumentException("商品不存在");
        }

        // 验证库存
        if (quantity > product.getStock()) {
            throw new IllegalArgumentException("商品库存不足");
        }

        // 检查购物车中是否已有该商品
        int currentQuantity = cartDao.getCartItemQuantity(userId, productId);
        if (currentQuantity > 0) {
            // 更新数量
            cartDao.updateCartItem(userId, productId, currentQuantity + quantity);
        } else {
            // 添加新商品
            cartDao.addCartItem(userId, productId, quantity, true);
        }
    }

    @Override
    public void updateCartItem(Long userId, Long productId, Integer quantity) throws Exception {
        if (quantity <= 0) {
            cartDao.removeCartItem(userId, productId);
        } else {
            // 验证库存
            Product product = productDao.getProductById(productId);
            if (quantity > product.getStock()) {
                throw new IllegalArgumentException("商品库存不足");
            }
            cartDao.updateCartItem(userId, productId, quantity);
        }
    }

    @Override
    public void removeFromCart(Long userId, Long productId) throws Exception {
        cartDao.removeCartItem(userId, productId);
    }

    @Override
    public Map<Long, CartItem> getUserCart(Long userId) throws Exception {
        Map<Long, CartItem> cart = new HashMap<>();
        // 从数据库获取购物车项
        List<CartItem> cartItems = cartDao.getUserCartItems(userId);

        for (CartItem item : cartItems) {
            CartItem cartItem = new CartItem();
            cartItem.setProductId(item.getProductId());
            cartItem.setPrice(item.getPrice());
            cartItem.setMainImage(item.getMainImage());
            cartItem.setQuantity(item.getQuantity());
            cart.put(cartItem.getProductId(), cartItem);
        }
        return cart;
    }

    @Override
    public double calculateTotalPrice(Map<Long, CartItem> cart) {
        return cart.values().stream()
                .mapToDouble(item -> item.getPrice() * item.getQuantity())
                .sum();
    }

    @Override
    public void clearCart(Long userId) throws Exception {
        cartDao.clearCart(userId);
    }
}