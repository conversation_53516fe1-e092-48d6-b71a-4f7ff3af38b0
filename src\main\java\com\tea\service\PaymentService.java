package com.tea.service;

import com.tea.entity.Order;
import com.tea.entity.User;

public interface PaymentService {
    /**
     * 处理订单支付
     * @param orderId 订单ID
     * @param user 支付用户
     * @return 支付是否成功
     * @throws Exception 支付过程中发生的异常
     */
    boolean processPayment(Long orderId, User user) throws Exception;

    /**
     * 验证支付状态
     * @param orderId 订单ID
     * @return 支付验证结果
     * @throws Exception 验证过程中发生的异常
     */
    String verifyPayment(Long orderId) throws Exception;

    /**
     * 查询订单支付状态
     * @param orderId 订单ID
     * @return 支付状态
     * @throws Exception 查询过程中发生的异常
     */
    String getPaymentStatus(Long orderId) throws Exception;

    /**
     * 获取订单信息
     * @param orderId 订单ID
     * @return 订单对象
     * @throws Exception 查询过程中发生的异常
     */
    Order getOrderById(Long orderId) throws Exception;
}