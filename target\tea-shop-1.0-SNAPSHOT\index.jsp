<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<html>
<head>
    <title>茶品商城</title>

    <link rel="stylesheet" href="css/style.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <nav class="header">
        <div class="nav-container">
            <h1 class="logo">茶韵小铺</h1>
            <div class="nav-links">
                <a href="/index.jsp">首页</a>
                <a href="/cart.jsp">购物车</a>
                <a href="/order.jsp">订单</a>

            </div>
            <div class="auth-user-container">
                <c:if test="${not empty sessionScope.username}">
                    <img src="/images/default-avatar.png" alt="用户头像" class="user-avatar">
                    <span class="user-name">${sessionScope.username}</span>
                    <a href="/logout" class="auth-link">退出登录</a>
                </c:if>
                <c:if test="${empty sessionScope.username}">
                    <a href="/login.jsp" class="auth-link">登录</a>
                    <a href="/register.jsp" class="auth-link">注册</a>
                </c:if>
            </div>
        </div>
    </nav>

    <div class="container">
        <div class="category-grid">

            <div class="category-card" data-category-id="1">
                <a href="javascript:void(0)" class="category-link">
                    <div class="category-image" style="background-image: url('/images/西湖龙井.jpg')"></div>
                    <h3 class="category-name">绿茶</h3>
                    <p class="category-desc">新鲜清香的绿茶系列</p>
                </a>
            </div>
            <div class="category-card" data-category-id="2">
                <a href="javascript:void(0)" class="category-link">
                    <div class="category-image" style="background-image: url('/images/功夫红茶.jpg')"></div>
                    <h3 class="category-name">红茶</h3>
                    <p class="category-desc">醇厚浓郁的红茶选择</p>
                </a>
            </div>
            <div class="category-card" data-category-id="3">
                <a href="javascript:void(0)" class="category-link">
                    <div class="category-image" style="background-image: url('/images/白毫银针.jpg')"></div>
                    <h3 class="category-name">白茶</h3>
                    <p class="category-desc">清润雅致的白茶之选</p>
                </a>
            </div>
            <div class="category-card" data-category-id="4">
                <a href="javascript:void(0)" class="category-link">
                    <div class="category-image" style="background-image: url('/images/百合花茶.jpg')"></div>
                    <h3 class="category-name">花茶</h3>
                    <p class="category-desc">芬芳馥郁的花茶之韵</p>
                </a>
            </div>
        </div>
        <div class="product-grid" id="productGrid"></div>
    </div>

    <script>
        $(document).ready(function() {
            // 类别卡片点击事件
            $('.category-card').click(function() {
                $('.category-btn').removeClass('active');
                $(this).addClass('active');
                var categoryId = $(this).data('category-id');
                loadProducts(categoryId);
            });

            // 加载商品函数
            function loadProducts(categoryId) {
                $.ajax({
                url: "/products",
                data: { categoryId: categoryId },
                type: "GET",
                dataType: "json",
                success: function(data) {
                    if (data.length > 0) {
                    var html = '';
                    $.each(data, function(index, product) {
                        html += '<a href="/product-detail?productId=' + product.productId + '" class="product-link">';
                        html += '<div class="product-card">';
                        // 产品图片映射表（扩展至38个产品）
                        const productImages = {
                            1: '/images/碧螺春.jpg',   2: '/images/正山小种.jpg',   3: '/images/西湖龙井.jpg',
                            4: '/images/太平猴魁.jpg',   5: '/images/黄山毛峰.jpg',   6: '/images/信阳毛尖.jpg',
                            7: '/images/都匀毛尖.jpg',   8: '/images/刘安瓜片.jpg',   9: '/images/蒙顶甘露.jpg',
                            10: '/images/安吉白茶.jpg', 11: '/images/恩施玉露.jpg', 12: '/images/祁门红茶.jpg',
                            13: '/images/正山小种.jpg', 14: '/images/金俊眉.jpg', 15: '/images/银俊眉.jpg',
                            16: '/images/九曲红眉.jpg', 17: '/images/宁红功夫.jpg', 18: '/images/宜红功夫.jpg',
                            19: '/images/政和工夫.jpg', 20: '/images/遵义红.jpg', 21: '/images/日照红梅.jpg',
                            22: '/images/白毫银针.jpg', 23: '/images/老白茶.jpg', 24: '/images/福鼎白茶.jpg',
                            25: '/images/白牡丹.jpg', 26: '/images/政和白茶.jpg', 27: '/images/曼谷白茶.jpg',
                            28: '/images/贡眉.jpg', 29: '/images/寿眉.jpg', 30: '/images/茉莉花茶.jpg',
                            31: '/images/白兰花茶.jpg', 32: '/images/珠兰花茶.jpg', 33: '/images/菊花茶.jpg',
                            34: '/images/玫瑰花茶.jpg', 35: '/images/桂花茶.jpg', 36: '/images/桃花茶.jpg',
                            37: '/images/金银花茶.jpg', 38: '/images/百合花茶.jpg'
                        };
                        // 获取图片路径（默认使用default-tea.jpg）
                        const imgSrc = "/images/"+product.name+".jpg" || '/images/default-tea.jpg';
                        html += `<img src="`+imgSrc+`" alt="${product.name}" class="product-img" >`;
                        html += '<div class="product-info">';
                        html += '<h3 class="product-name">' + product.name + '</h3>';
                        html += '<p class="product-desc">' + product.description + '</p>';
                        html += '<p class="product-price">' + product.price + '</p>';
                        html += '</div></div></a>';
                    });
                    $('#productGrid').html(html);
                    $('.category-grid').hide(); // 茶品存在时隐藏类别选项卡
                } else {
                    // 仅当非首页类别时显示提示（categoryId不为0）
                    if (categoryId !== 0) {
                        $('#productGrid').html('<p class="no-product">暂无茶品信息</p>');
                    } else {
                        $('#productGrid').html(''); // 首页不显示提示
                    }
                }
                },
                error: function(xhr) {
                    $('#productGrid').html('<p class="no-product">请求后端失败，请稍后再试</p>');
                }
            });
            }
        });
    </script>
        </div>
    </div>
</body>
</html>
