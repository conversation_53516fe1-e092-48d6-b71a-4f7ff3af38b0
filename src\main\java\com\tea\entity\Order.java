package com.tea.entity;

import java.sql.Timestamp;

public class Order {
    private long orderId;
    private long userId;
    private Timestamp orderDate;
    private int status;
    private double totalAmount;

    // 构造方法
    public Order() {}

    public Order(long userId, Timestamp orderDate, int status, double totalAmount) {
        this.userId = userId;
        this.orderDate = orderDate;
        this.status = status;
        this.totalAmount = totalAmount;
    }

    // Getters and Setters
    public long getOrderId() { return orderId; }
    public void setOrderId(long orderId) { this.orderId = orderId; }

    public long getUserId() { return userId; }
    public void setUserId(long userId) { this.userId = userId; }

    public Timestamp getOrderDate() { return orderDate; }
    public void setOrderDate(Timestamp orderDate) { this.orderDate = orderDate; }

    public int getStatus() { return status; }
    public void setStatus(int status) { this.status = status; }

    public double getTotalAmount() { return totalAmount; }
    public void setTotalAmount(double totalAmount) { this.totalAmount = totalAmount; }

    
}