package com.tea.service;

import com.tea.entity.User;
import java.sql.SQLException;

public interface UserService {
    /**
     * 用户登录验证
     * @param username 用户名
     * @param password 密码
     * @return 登录成功返回User对象，失败返回null
     * @throws SQLException 数据库操作异常
     */
    User login(String username, String password) throws SQLException;

    /**
     * 检查用户名是否已存在
     * @param username 用户名
     * @return 存在返回true，不存在返回false
     * @throws SQLException 数据库操作异常
     */
    boolean checkUsernameExists(String username) throws SQLException;

    /**
     * 用户注册
     * @param user 用户对象
     * @return 注册成功返回true，失败返回false
     * @throws SQLException 数据库操作异常
     */
    boolean register(User user) throws SQLException;
}