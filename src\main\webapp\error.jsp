<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>错误 - 茶品商城</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        body {
            background-color: #f8f9fa;
            color: #333;
            line-height: 1.6;
            padding: 20px;
        }
        .container {
            max-width: 600px;
            margin: 50px auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            overflow: hidden;
            text-align: center;
        }
        .header {
            background-color: #e74c3c;
            color: white;
            padding: 30px;
        }
        .header h1 {
            font-size: 48px;
            margin-bottom: 10px;
        }
        .header p {
            font-size: 18px;
            opacity: 0.9;
        }
        .content {
            padding: 40px 30px;
        }
        .error-message {
            font-size: 18px;
            color: #e74c3c;
            margin-bottom: 30px;
            padding: 20px;
            background-color: #fadbd8;
            border-radius: 8px;
        }
        .actions {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s;
            cursor: pointer;
        }
        .btn-primary {
            background-color: #3498db;
            color: white;
        }
        .btn-primary:hover {
            background-color: #2980b9;
            transform: translateY(-2px);
        }
        .btn-secondary {
            background-color: #95a5a6;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #7f8c8d;
            transform: translateY(-2px);
        }
        .icon {
            font-size: 64px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="icon">⚠️</div>
            <h1>出错了</h1>
            <p>很抱歉，页面出现了问题</p>
        </div>
        
        <div class="content">
            <c:choose>
                <c:when test="${not empty errorMsg}">
                    <div class="error-message">
                        ${errorMsg}
                    </div>
                </c:when>
                <c:when test="${not empty param.msg}">
                    <div class="error-message">
                        ${param.msg}
                    </div>
                </c:when>
                <c:otherwise>
                    <div class="error-message">
                        页面访问出现错误，请稍后重试
                    </div>
                </c:otherwise>
            </c:choose>
            
            <div class="actions">
                <a href="javascript:history.back()" class="btn btn-secondary">返回上页</a>
                <a href="/index.jsp" class="btn btn-primary">返回首页</a>
            </div>
        </div>
    </div>
</body>
</html>
