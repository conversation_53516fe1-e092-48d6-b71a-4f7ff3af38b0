package com.tea.servlet;

import com.tea.service.ReviewService;
import com.tea.service.impl.ReviewServiceImpl;
import com.tea.entity.Review;
import com.tea.entity.User;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.Date;
import java.util.List;

@WebServlet("/comment")
public class CommentServlet extends HttpServlet {
    private ReviewService reviewService = new ReviewServiceImpl();

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        HttpSession session = request.getSession();
        User user = (User) session.getAttribute("user");
        if (user == null) {
            String encodedMessage = URLEncoder.encode("请先登录后再评论", "UTF-8");
            response.sendRedirect("/login.jsp?message=" + encodedMessage);
            return;
        }

        long productId = 0;
        try {
            productId = Long.parseLong(request.getParameter("productId"));
            String content = request.getParameter("content");

            Review review = new Review();
            review.setProductId(productId);
            review.setUserId(user.getId());
            review.setUsername(user.getUsername());
            review.setContent(content);
            review.setCreateTime(new Date());
            // 获取用户购买该商品的订单ID（实际应用中需要实现订单查询逻辑）
            // 临时解决方案：如果没有订单ID，设置为null或移除该字段


            reviewService.addReview(review);
            response.sendRedirect("/product-detail?productId=" + productId);
        } catch (Exception e) {
            e.printStackTrace();
            response.sendRedirect("/product-detail.jsp?productId=" + productId + "&error=评论提交失败，请重试");
        }
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        try {
            long productId = Long.parseLong(request.getParameter("productId"));
            List<Review> reviews = reviewService.getReviewsByProductId(productId);
            request.setAttribute("comments", reviews);
            request.getRequestDispatcher("/product-detail.jsp").forward(request, response);
        } catch (Exception e) {
            e.printStackTrace();
            request.setAttribute("error", "获取评论失败");
            request.getRequestDispatcher("/product-detail.jsp").forward(request, response);
        }
    }
}