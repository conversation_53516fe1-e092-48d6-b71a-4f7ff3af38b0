package com.tea.service;

import com.tea.entity.Product;
import java.util.List;
import java.util.Map;

public interface ProductService {
    /**
     * 根据分类ID查询商品
     * @param categoryId 分类ID，为null时查询所有商品
     * @return 商品列表，每个商品信息以Map形式返回
     * @throws Exception 查询过程中发生的异常
     */
    List<Product> getProductsByCategory(Long categoryId) throws Exception;

    /**
     * 查询所有商品
     * @return 商品列表，每个商品信息以Map形式返回
     * @throws Exception 查询过程中发生的异常
     */
    List<Product> getAllProducts() throws Exception;

    /**
     * 根据商品ID查询商品详情
     * @param productId 商品ID
     * @return 商品详情Map
     * @throws Exception 查询过程中发生的异常
     */
    Product getProductById(Long productId) throws Exception;
}