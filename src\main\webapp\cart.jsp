<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<html>
<head>
    <title>购物车 - 茶品商城</title>
    <link rel="stylesheet" href="css/style.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <nav class="header">
        <div class="nav-container">
            <h1 class="logo">茶韵小铺</h1>
            <div class="nav-links">
                <a href="/index.jsp">首页</a>
                <a href="/cart.jsp">购物车</a>
                <a href="/order?action=list">订单</a>

            </div>
        </div>
    </nav>
    <div class="container">
        <h1>我的购物车</h1>
        <c:if test="${empty sessionScope.cart}">
            <p class="empty-cart">购物车为空</p>
        </c:if>
        <c:if test="${not empty sessionScope.cart}">
            <table class="cart-table">
                <thead>
                    <tr>
                        <th>商品</th>
                        <th>价格</th>
                        <th>数量</th>
                        <th>小计</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <c:forEach items="${sessionScope.cart}" var="entry" varStatus="status">
                        <tr>
                            <td class="product-cell">
                                <img src="${entry.value.mainImage}" alt="${entry.value.name}" class="cart-image">
                                ${entry.value.name}
                            </td>
                            <td>￥${entry.value.price}</td>
                            <td>
                                <form action="/cart" method="post" class="quantity-form">
                                    <input type="hidden" name="action" value="update">
                                    <input type="hidden" name="productId" value="${entry.key}">
                                    <input type="number" name="quantity" value="${entry.value.quantity}" min="1" class="quantity-input">
                                    <button type="submit" class="btn btn-small">更新</button>
                                </form>
                            </td>
                            <td>￥${entry.value.price * entry.value.quantity}</td>
                            <td>
                                <form action="/cart" method="post" class="remove-form">
                                    <input type="hidden" name="action" value="remove">
                                    <input type="hidden" name="productId" value="${entry.key}">
                                    <button type="submit" class="btn btn-small btn-danger">移除</button>
                                </form>
                            </td>
                        </tr>
                    </c:forEach>
                </tbody>
            </table>
            <div class="total-section">
                <p>总金额：￥${sessionScope.totalPrice}</p>
                <form action="/order" method="post" class="checkout-form">
                    <div class="form-status"></div>
                    <input type="hidden" name="action" value="create">
                    <button type="submit" class="btn btn-primary">结算下单</button>
                </form>
            </div>
        </c:if>
        <a href="/" class="back-link">返回首页</a>
    </div>
</body>
</html>