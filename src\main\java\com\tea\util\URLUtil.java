package com.tea.util;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

/**
 * URL工具类，用于处理URL编码和重定向
 */
public class URLUtil {
    
    /**
     * 对字符串进行URL编码
     * @param str 要编码的字符串
     * @return 编码后的字符串，如果编码失败返回原字符串
     */
    public static String encode(String str) {
        if (str == null || str.trim().isEmpty()) {
            return str;
        }
        try {
            return URLEncoder.encode(str, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            // UTF-8编码应该总是支持的，如果失败就返回原字符串
            System.err.println("URL编码失败: " + e.getMessage());
            return str;
        }
    }
    
    /**
     * 构建带有错误消息的重定向URL
     * @param baseUrl 基础URL
     * @param errorMessage 错误消息
     * @return 完整的重定向URL
     */
    public static String buildErrorRedirectUrl(String baseUrl, String errorMessage) {
        String encodedError = encode(errorMessage);
        if (baseUrl.contains("?")) {
            return baseUrl + "&error=" + encodedError;
        } else {
            return baseUrl + "?error=" + encodedError;
        }
    }
    
    /**
     * 构建带有成功消息的重定向URL
     * @param baseUrl 基础URL
     * @param successMessage 成功消息
     * @return 完整的重定向URL
     */
    public static String buildSuccessRedirectUrl(String baseUrl, String successMessage) {
        String encodedSuccess = encode(successMessage);
        if (baseUrl.contains("?")) {
            return baseUrl + "&success=" + encodedSuccess;
        } else {
            return baseUrl + "?success=" + encodedSuccess;
        }
    }
    
    /**
     * 构建带有消息的重定向URL
     * @param baseUrl 基础URL
     * @param paramName 参数名（如msg、error、success等）
     * @param message 消息内容
     * @return 完整的重定向URL
     */
    public static String buildRedirectUrl(String baseUrl, String paramName, String message) {
        String encodedMessage = encode(message);
        if (baseUrl.contains("?")) {
            return baseUrl + "&" + paramName + "=" + encodedMessage;
        } else {
            return baseUrl + "?" + paramName + "=" + encodedMessage;
        }
    }
}
