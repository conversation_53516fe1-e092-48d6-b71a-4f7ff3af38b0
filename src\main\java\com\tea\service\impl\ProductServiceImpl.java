package com.tea.service.impl;

import com.tea.dao.ProductDao;
import com.tea.service.ProductService;
import java.util.List;
import com.tea.entity.Product;

public class ProductServiceImpl implements ProductService {
    private ProductDao productDao = new ProductDao();

    @Override
    public List<Product> getProductsByCategory(Long categoryId) throws Exception {
        return productDao.getByCategory(categoryId.intValue());
    }

    @Override
    public List<Product> getAllProducts() throws Exception {
        return productDao.getAll();
    }

    @Override
    public Product getProductById(Long productId) throws Exception {
        return productDao.getProductById(productId);
    }
}