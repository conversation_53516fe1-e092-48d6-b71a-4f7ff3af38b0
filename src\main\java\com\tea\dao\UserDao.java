package com.tea.dao;

import com.tea.entity.User;
import com.tea.util.DBUtil;

import java.sql.*;

public class UserDao {
    public boolean checkUsernameExists(String username) throws SQLException {
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        try {
            conn = DBUtil.getConnection();
            String checkSql = "SELECT * FROM user WHERE username = ?";
            pstmt = conn.prepareStatement(checkSql);
            pstmt.setString(1, username);
            rs = pstmt.executeQuery();
            return rs.next();
        } finally {
            DBUtil.close(conn, pstmt, rs);
        }
    }

    public boolean insert(User user) throws SQLException {
        Connection conn = null;
        PreparedStatement pstmt = null;
        try {
            conn = DBUtil.getConnection();
            String insertSql = "INSERT INTO user (username, password, avatar, user_id) VALUES (?, ?, ?, ?)";
            pstmt = conn.prepareStatement(insertSql);
            pstmt.setString(1, user.getUsername());
            pstmt.setString(2, user.getPassword());
            pstmt.setString(3, user.getAvatar());
            pstmt.setLong(4, user.getId());
            int rows = pstmt.executeUpdate();
            return rows > 0;
        } finally {
            DBUtil.close(conn, pstmt, null);
        }
    }

    public boolean register(User user) throws SQLException {
        if (checkUsernameExists(user.getUsername())) {
            return false; // 用户名已存在
        }
        return insert(user);
    }

    public User getUserByUsernameAndPassword(String username, String password) throws SQLException {
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        try {
            conn = DBUtil.getConnection();
            String sql = "SELECT user_id, username, password, email, phone FROM user WHERE username = ? AND password = ?";
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, username);
            pstmt.setString(2, password);
            rs = pstmt.executeQuery();
            if (rs.next()) {
                User user = new User();
                user.setId(rs.getLong("user_id"));
                user.setUsername(rs.getString("username"));
                user.setPassword(rs.getString("password"));
                user.setEmail(rs.getString("email"));
                user.setPhone(rs.getString("phone"));
                return user;
            }
            return null;
        } finally {
            DBUtil.close(conn, pstmt, rs);
        }
    }

    public boolean validate(String username, String password) {
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = com.tea.util.DBUtil.getConnection();
            String sql = "SELECT * FROM user WHERE username = ? AND password = ?";
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, username);
            pstmt.setString(2, password);
            rs = pstmt.executeQuery();
            
            return rs.next();
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        } finally {
            com.tea.util.DBUtil.close(conn, pstmt, rs);
        }
    }
}