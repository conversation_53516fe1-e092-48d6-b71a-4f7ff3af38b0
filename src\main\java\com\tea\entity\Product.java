package com.tea.entity;

public class Product {
    private Long productId;
    private String name;
    private String description;
    private double price;
    private String mainImage;
    private Long categoryId;
    private int stock;

    public Product() {
    }

    public Product(Long productId, String name, String description, double price, String mainImage, Long categoryId, int stock) {
        this.productId = productId;
        this.name = name;
        this.description = description;
        this.price = price;
        this.mainImage = mainImage;
        this.categoryId = categoryId;
        this.stock = stock;
    }

    // Getters and Setters
    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public double getPrice() {
        return price;
    }

    public void setPrice(double price) {
        this.price = price;
    }

    public String getMainImage() {
        return mainImage;
    }

    public void setMainImage(String mainImage) {
        this.mainImage = mainImage;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public int getStock() {
        return stock;
    }

    public void setStock(int stock) {
        this.stock = stock;
    }
}