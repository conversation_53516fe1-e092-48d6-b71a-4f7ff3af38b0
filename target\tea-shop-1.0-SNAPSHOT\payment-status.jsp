<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付状态查询 - 茶品商城</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        body {
            background-color: #f8f9fa;
            color: #333;
            line-height: 1.6;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.05);
            overflow: hidden;
        }
        .header {
            background-color: #3498db;
            color: white;
            padding: 25px;
            text-align: center;
        }
        .header h1 {
            font-size: 24px;
            margin-bottom: 10px;
        }
        .status-container {
            padding: 30px;
        }
        .status-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        .order-number {
            font-size: 18px;
            font-weight: bold;
        }
        .refresh-btn {
            padding: 8px 16px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        .refresh-btn:hover {
            background-color: #2980b9;
        }
        .status-card {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .status-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .status-content {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        .status-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            flex-shrink: 0;
        }
        .status-pending {
            background-color: #f1c40f;
            color: white;
        }
        .status-processing {
            background-color: #3498db;
            color: white;
        }
        .status-success {
            background-color: #2ecc71;
            color: white;
        }
        .status-failed {
            background-color: #e74c3c;
            color: white;
        }
        .status-text {
            font-size: 16px;
            font-weight: bold;
        }
        .status-description {
            color: #7f8c8d;
            font-size: 14px;
            margin-top: 5px;
        }
        .order-info {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .order-info h3 {
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .info-row:last-child {
            border-bottom: none;
        }
        .label {
            color: #7f8c8d;
        }
        .value {
            font-weight: bold;
        }
        .timeline {
            position: relative;
            margin: 30px 0;
            padding-left: 30px;
        }
        .timeline::before {
            content: '';
            position: absolute;
            left: 10px;
            top: 0;
            bottom: 0;
            width: 2px;
            background-color: #eee;
        }
        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -34px;
            top: 0;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: white;
            border: 2px solid #3498db;
        }
        .timeline-item.active::before {
            background-color: #3498db;
        }
        .timeline-content {
            background-color: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        .timeline-date {
            font-size: 12px;
            color: #7f8c8d;
            margin-top: 5px;
        }
        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 30px;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            text-decoration: none;
            text-align: center;
            transition: all 0.3s;
        }
        .btn-primary {
            background-color: #3498db;
            color: white;
        }
        .btn-primary:hover {
            background-color: #2980b9;
        }
        .btn-secondary {
            background-color: #95a5a6;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #7f8c8d;
        }
        .tips {
            background-color: #f1f9f7;
            border-left: 4px solid #2ecc71;
            padding: 15px;
            margin-top: 20px;
            border-radius: 4px;
            font-size: 14px;
            color: #27ae60;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>支付状态查询</h1>
            <p>实时查看您的订单支付处理进度</p>
        </div>

        <div class="status-card">
            <div class="status-content">
                <div class="status-icon status-${statusClass}">
                    <c:choose>
                        <c:when test="${status == 'pending'}">⏳</c:when>
                        <c:when test="${status == 'processing'}">🔄</c:when>
                        <c:when test="${status == 'success'}">✓</c:when>
                        <c:otherwise>⚠️</c:otherwise>
                    </c:choose>
                </div>
                <div>
                    <div class="status-text">
                        <c:choose>
                            <c:when test="${status == 'pending'}">待支付</c:when>
                            <c:when test="${status == 'processing'}">支付处理中</c:when>
                            <c:when test="${status == 'success'}">支付成功</c:when>
                            <c:when test="${status == 'failed'}">支付失败</c:when>
                            <c:otherwise>未知状态</c:otherwise>
                        </c:choose>
                    </div>
                    <div class="status-description">
                        <c:choose>
                            <c:when test="${status == 'pending'}">您的订单已创建，请尽快完成支付以确保库存</c:when>
                            <c:when test="${status == 'processing'}">支付请求已接收，正在处理中...</c:when>
                            <c:when test="${status == 'success'}">支付成功！订单状态已更新为待发货</c:when>
                            <c:when test="${status == 'failed'}">支付未完成，请检查支付信息并重试</c:when>
                            <c:otherwise>无法获取订单状态信息</c:otherwise>
                        </c:choose>
                    </div>
                </div>
            </div>
        </div>

        <div class="order-info">
            <h3>订单信息</h3>
            <div class="info-row">
                <span class="label">订单编号:</span>
                <span class="value">${order.orderId}</span>
            </div>
            <div class="info-row">
                <span class="label">下单时间:</span>
                <span class="value">${order.orderDate}</span>
            </div>
            <div class="info-row">
                <span class="label">订单金额:</span>
                <span class="value">¥${order.totalAmount}</span>
            </div>
            <div class="info-row">
                <span class="label">支付方式:</span>
                <span class="value">${paymentMethod}</span>
            </div>
        </div>

        <div class="timeline">
            <div class="timeline-item active">
                <div class="timeline-content">
                    <strong>订单创建</strong>
                    <div class="timeline-date">${order.createTime}</div>
                </div>
            </div>
            <div class="timeline-item ${status == 'processing' || status == 'success' || status == 'failed' ? 'active' : ''}">
                <div class="timeline-content">
                    <strong>支付处理中</strong>
                    <div class="timeline-date">${paymentTime}</div>
                </div>
            </div>
            <div class="timeline-item ${status == 'success' ? 'active' : ''}">
                <div class="timeline-content">
                    <strong>支付完成</strong>
                    <div class="timeline-date">${completionTime}</div>
                </div>
            </div>
        </div>

        <div class="action-buttons">
            <c:choose>
                <c:when test="${status == 'pending'}">
                    <a href="/payment?action=process&orderId=${order.orderId}" class="btn btn-primary">立即支付</a>
                </c:when>
                <c:when test="${status == 'failed'}">
                    <a href="/payment?action=process&orderId=${order.orderId}" class="btn btn-primary">重新支付</a>
                </c:when>
                <c:when test="${status == 'success'}">
                    <a href="/order?action=view&orderId=${order.orderId}" class="btn btn-primary">查看订单详情</a>
                </c:when>
                <c:otherwise>
                    <a href="javascript:history.back()" class="btn btn-primary">返回上一页</a>
                </c:otherwise>
            </c:choose>
            <a href="/index.jsp" class="btn btn-secondary">返回首页</a>
        </div>
    </div>
</body>
</html>