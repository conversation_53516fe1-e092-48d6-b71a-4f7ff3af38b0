package com.tea.service;

import com.tea.entity.CartItem;
import java.util.Map;

public interface CartService {
    /**
     * 添加商品到购物车
     */
    void addToCart(Long userId, Long productId, Integer quantity) throws Exception;

    /**
     * 更新购物车商品数量
     */
    void updateCartItem(Long userId, Long productId, Integer quantity) throws Exception;

    /**
     * 从购物车移除商品
     */
    void removeFromCart(Long userId, Long productId) throws Exception;

    /**
     * 获取用户购物车
     */
    Map<Long, CartItem> getUserCart(Long userId) throws Exception;

    /**
     * 计算购物车总价
     */
    double calculateTotalPrice(Map<Long, CartItem> cart);

    /**
     * 清空购物车
     */
    void clearCart(Long userId) throws Exception;
}