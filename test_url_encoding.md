# URL编码修复测试指南

## 问题描述
之前的错误是由于在HTTP重定向URL中包含了未编码的中文字符导致的：
```
java.lang.IllegalArgumentException: 代码点[24,744]处的Unicode字符[您]无法编码，因为它超出了允许的0到255范围。
```

## 修复内容

### 1. 创建了URLUtil工具类
- 统一处理URL编码
- 提供便捷的重定向URL构建方法
- 自动处理编码异常

### 2. 修复的Servlet
- **CommentServlet**: 评论提交相关的所有重定向
- **OrderServlet**: 订单操作相关的重定向
- **CartServlet**: 购物车操作相关的重定向

### 3. 修复的重定向场景
- 用户未登录时的重定向
- 评论提交成功/失败的重定向
- 表单验证失败的重定向
- 业务逻辑错误的重定向

## 测试步骤

### 测试1: 评论功能URL编码
1. 访问商品详情页面
2. 在未登录状态下尝试提交评论
3. **预期结果**: 正确重定向到登录页面，不出现编码错误

### 测试2: 重复评论检查
1. 登录用户
2. 对同一商品提交第一次评论（应该成功）
3. 再次尝试提交评论
4. **预期结果**: 显示"您已经对该商品发表过评论了"，不出现编码错误

### 测试3: 表单验证
1. 登录用户
2. 尝试提交空内容的评论
3. 尝试提交未选择评分的评论
4. **预期结果**: 显示相应的中文错误消息，不出现编码错误

### 测试4: 购物车操作
1. 在未登录状态下尝试添加商品到购物车
2. **预期结果**: 正确重定向到登录页面，显示中文提示消息

### 测试5: 订单操作
1. 在未登录状态下尝试访问订单页面
2. **预期结果**: 正确重定向到登录页面，显示中文提示消息

## 验证方法

### 1. 检查服务器日志
- 不应该再出现`IllegalArgumentException`关于Unicode字符编码的错误
- 不应该出现URL编码相关的警告

### 2. 检查浏览器网络面板
- 重定向的URL应该正确编码中文字符
- 例如："您已经对该商品发表过评论了" 应该编码为 "%E6%82%A8%E5%B7%B2%E7%BB%8F%E5%AF%B9%E8%AF%A5%E5%95%86%E5%93%81%E5%8F%91%E8%A1%A8%E8%BF%87%E8%AF%84%E8%AE%BA%E4%BA%86"

### 3. 检查页面显示
- 错误消息和成功消息应该正确显示中文
- 页面应该正常加载，不出现乱码

## 代码改进点

### URLUtil工具类的优势
1. **统一编码处理**: 所有URL编码都通过同一个方法处理
2. **异常安全**: 即使编码失败也不会导致应用崩溃
3. **便捷方法**: 提供了构建错误和成功重定向URL的便捷方法
4. **可维护性**: 集中管理URL编码逻辑，便于维护和修改

### 使用示例
```java
// 旧方式（容易出错）
String encodedError = URLEncoder.encode("评论内容不能为空", "UTF-8");
response.sendRedirect("/product-detail?productId=" + productId + "&error=" + encodedError);

// 新方式（安全可靠）
String redirectUrl = URLUtil.buildErrorRedirectUrl("/product-detail?productId=" + productId, "评论内容不能为空");
response.sendRedirect(redirectUrl);
```

## 如果仍然出现问题

### 检查清单
1. 确保所有Servlet都已重新编译
2. 重启应用服务器
3. 清除浏览器缓存
4. 检查是否还有其他Servlet使用了未编码的中文重定向

### 调试方法
1. 在URLUtil.encode()方法中添加日志输出
2. 检查具体哪个重定向URL导致问题
3. 验证UTF-8编码是否正确设置

## 总结
通过创建URLUtil工具类和修复所有相关的Servlet，现在所有包含中文字符的重定向URL都会被正确编码，避免了Tomcat的Unicode编码错误。这个修复不仅解决了当前的问题，还为将来的开发提供了一个可靠的URL编码解决方案。
