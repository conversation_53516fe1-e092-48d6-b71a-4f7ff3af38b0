package com.tea.service.impl;

import com.tea.dao.ReviewDao;
import com.tea.entity.Review;
import com.tea.service.ReviewService;
import java.util.List;

public class ReviewServiceImpl implements ReviewService {
    private final ReviewDao reviewDao = new ReviewDao();

    @Override
    public int addReview(Review review) throws Exception {
        // 可以在这里添加业务逻辑，如评论内容验证、权限检查等
        return reviewDao.addReview(review);
    }

    @Override
    public List<Review> getReviewsByProductId(Long productId) throws Exception {
        // 可以在这里添加缓存逻辑或数据过滤等业务处理
        return reviewDao.getReviewsByProductId(productId);
    }
}