package com.tea.dao;

import com.tea.entity.Review;
import com.tea.util.DBUtil;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class ReviewDao {
    // 添加评论
    public int addReview(Review review) throws Exception {
        String sql = "INSERT INTO review (product_id, user_id, content, rating,order_id) VALUES (?,?, ?, ?, ?)";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setLong(1, review.getProductId());
            pstmt.setLong(2, review.getUserId());
            pstmt.setString(3, review.getContent());
            pstmt.setInt(4, 5);
            pstmt.setInt(5, 0);
            return pstmt.executeUpdate();
        }
    }

    // 根据商品ID查询评论
    public List<Review> getReviewsByProductId(Long productId) throws Exception {
        List<Review> reviews = new ArrayList<>();
        String sql = "SELECT review_id, product_id, user_id, content, rating FROM review WHERE product_id = ? ORDER BY review_id DESC";
        try (Connection conn = DBUtil.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setLong(1, productId);
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    Review review = new Review();
                    review.setReviewId(rs.getLong("review_id"));
                    review.setOrderId(rs.getLong("order_id"));
                    review.setProductId(rs.getLong("product_id"));
                    review.setUserId(rs.getLong("user_id"));
                    review.setContent(rs.getString("content"));
                    reviews.add(review);
                }
            }
        }
        return reviews;
    }
}